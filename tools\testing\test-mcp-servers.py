#!/usr/bin/env python3
"""
MCP服务器测试脚本
测试所有已安装的MCP服务器是否正常工作
"""

import subprocess
import sys
import json
import time
from pathlib import Path

def test_node_server(server_name, script_path):
    """测试Node.js MCP服务器"""
    print(f"🧪 测试 {server_name} 服务器...")
    try:
        # 启动服务器进程
        process = subprocess.Popen(
            ["node", script_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=Path.cwd()
        )
        
        # 等待一小段时间让服务器启动
        time.sleep(2)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print(f"✅ {server_name} 服务器启动成功")
            process.terminate()
            process.wait()
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ {server_name} 服务器启动失败")
            if stderr:
                print(f"错误: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ {server_name} 服务器测试异常: {e}")
        return False

def test_python_server(server_name, module_name):
    """测试Python MCP服务器"""
    print(f"🧪 测试 {server_name} 服务器...")
    try:
        # 启动服务器进程
        process = subprocess.Popen(
            ["python", "-m", module_name],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=Path.cwd()
        )
        
        # 等待一小段时间让服务器启动
        time.sleep(2)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print(f"✅ {server_name} 服务器启动成功")
            process.terminate()
            process.wait()
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ {server_name} 服务器启动失败")
            if stderr:
                print(f"错误: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ {server_name} 服务器测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试MCP服务器...")
    print("=" * 50)
    
    results = {}
    
    # 测试Node.js服务器
    node_servers = [
        ("filesystem", "mcp/servers/src/filesystem/dist/index.js"),
        ("everything", "mcp/servers/src/everything/dist/index.js"),
        ("memory", "mcp/servers/src/memory/dist/index.js"),
        ("sequentialthinking", "mcp/servers/src/sequentialthinking/dist/index.js")
    ]
    
    for name, path in node_servers:
        results[name] = test_node_server(name, path)
        print()
    
    # 测试Python服务器
    python_servers = [
        ("fetch", "mcp_server_fetch"),
        ("git", "mcp_server_git"),
        ("time", "mcp_server_time")
    ]
    
    for name, module in python_servers:
        results[name] = test_python_server(name, module)
        print()
    
    # 输出测试结果
    print("=" * 50)
    print("📊 测试结果汇总:")
    print("=" * 50)
    
    success_count = 0
    total_count = len(results)
    
    for server, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{server:20} {status}")
        if success:
            success_count += 1
    
    print("=" * 50)
    print(f"总计: {success_count}/{total_count} 个服务器测试通过")
    
    if success_count == total_count:
        print("🎉 所有MCP服务器都正常工作！")
        print("\n下一步:")
        print("1. 在Claudia中添加这些MCP服务器")
        print("2. 参考 MCP_SERVERS_CONFIG.md 文件获取配置详情")
        return 0
    else:
        print("⚠️  部分MCP服务器存在问题，请检查安装和配置")
        return 1

if __name__ == "__main__":
    sys.exit(main())
