cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=C:\Users\<USER>\Desktop\claudia\src-tauri\target\debug\build\tauri-plugin-http-cd9ca90c63a912be\out\tauri-plugin-http-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_SCOPE_SCHEMA_PATH=C:\Users\<USER>\Desktop\claudia\src-tauri\target\debug\build\tauri-plugin-http-cd9ca90c63a912be\out\global-scope.json
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-http-2.4.4\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
