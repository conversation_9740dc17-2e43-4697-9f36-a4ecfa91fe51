{"rustc": 1842507548689473721, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2225463790103693989, "path": 12518800248535767337, "deps": [[561782849581144631, "html5ever", false, 18401330832412159134], [1200537532907108615, "url<PERSON><PERSON>n", false, 16100582569608480575], [3060637413840920116, "proc_macro2", false, 5373005460213438867], [3129130049864710036, "memchr", false, 17740640694557240316], [3150220818285335163, "url", false, 3744897104065425490], [3191507132440681679, "serde_untagged", false, 2634942138606306105], [4899080583175475170, "semver", false, 6587108540636782383], [5986029879202738730, "log", false, 16544253528419911208], [6213549728662707793, "serde_with", false, 950721767048450767], [6262254372177975231, "kuchiki", false, 17786285992327720300], [6606131838865521726, "ctor", false, 14477600700006865172], [6913375703034175521, "schemars", false, 4311322490016638138], [7170110829644101142, "json_patch", false, 15473634152578215941], [8319709847752024821, "uuid", false, 7428505576557014790], [9010263965687315507, "http", false, 15629939712500996649], [9451456094439810778, "regex", false, 2432167459552640842], [9689903380558560274, "serde", false, 8619023586440547223], [10806645703491011684, "thiserror", false, 983470577670415442], [11655476559277113544, "cargo_metadata", false, 6128397840544643348], [11989259058781683633, "dunce", false, 11426863789755588728], [13625485746686963219, "anyhow", false, 16521415306001099308], [14132538657330703225, "brotli", false, 17325694709048316288], [15367738274754116744, "serde_json", false, 4475620309241193984], [15609422047640926750, "toml", false, 1657660958671004585], [15622660310229662834, "walkdir", false, 4450029676203649764], [17146114186171651583, "infer", false, 8123314953593878708], [17155886227862585100, "glob", false, 16666287207728800018], [17186037756130803222, "phf", false, 1824253341993985191], [17990358020177143287, "quote", false, 9227116837396871820]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-68a271a2288535ca\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}