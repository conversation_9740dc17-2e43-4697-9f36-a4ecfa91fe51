@echo off
title Claudia with MCP - Ready to Configure
color 0A
echo.
echo ========================================
echo    🚀 Claudia MCP 配置助手
echo ========================================
echo.
echo 📋 MCP服务器部署状态: ✅ 完成
echo 🔧 已准备 7 个MCP服务器:
echo    📁 filesystem    - 文件系统操作
echo    🔍 everything    - 快速文件搜索  
echo    🧠 memory        - 内存管理
echo    🔄 sequentialthinking - 顺序思维
echo    🌐 fetch         - HTTP请求
echo    📦 git           - Git操作
echo    ⏰ time          - 时间工具
echo.
echo 🌐 Claudia地址: http://localhost:1420
echo 📖 配置指南: MANUAL_MCP_SETUP.md
echo 📁 配置文件: .mcp.json
echo.

REM 检查Claudia是否已经在运行
echo 🔍 检查Claudia运行状态...
curl -s http://localhost:1420 >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Claudia已在运行
    echo.
    echo 🎯 下一步: 在Claudia中配置MCP服务器
    echo    1. 打开 http://localhost:1420
    echo    2. 点击对话框底部的 MCP 选项
    echo    3. 选择 Add Server 或 Import
    echo    4. 参考 MANUAL_MCP_SETUP.md 进行配置
    echo.
    echo 🌐 正在打开Claudia...
    start http://localhost:1420
) else (
    echo ⚠️ Claudia未运行，正在启动...
    echo.
    
    REM 启动Claudia
    echo 🚀 启动Claudia开发服务器...
    start "Claudia Dev Server" cmd /k "npm run tauri dev"
    
    REM 等待服务器启动
    echo ⏳ 等待服务器启动...
    timeout /t 10 /nobreak >nul
    
    REM 打开浏览器
    echo 🌐 正在打开Claudia...
    start http://localhost:1420
)

echo.
echo ========================================
echo    📋 配置清单
echo ========================================
echo.
echo 在Claudia MCP设置中添加以下服务器:
echo.
echo 📁 filesystem:
echo    Command: node
echo    Args: C:\Users\<USER>\Desktop\claudia\mcp\servers\src\filesystem\dist\index.js
echo.
echo 🔍 everything:
echo    Command: node  
echo    Args: C:\Users\<USER>\Desktop\claudia\mcp\servers\src\everything\dist\index.js
echo.
echo 🧠 memory:
echo    Command: node
echo    Args: C:\Users\<USER>\Desktop\claudia\mcp\servers\src\memory\dist\index.js
echo.
echo 🔄 sequentialthinking:
echo    Command: node
echo    Args: C:\Users\<USER>\Desktop\claudia\mcp\servers\src\sequentialthinking\dist\index.js
echo.
echo 🌐 fetch:
echo    Command: python
echo    Args: -m mcp_server_fetch
echo.
echo 📦 git:
echo    Command: python
echo    Args: -m mcp_server_git
echo.
echo ⏰ time:
echo    Command: python
echo    Args: -m mcp_server_time
echo.
echo ========================================
echo    🎉 准备完成！
echo ========================================
echo.
echo 💡 提示:
echo    - 所有服务器使用 stdio 传输方式
echo    - 推荐使用 project 作用域
echo    - 配置完成后测试连接状态
echo.
echo 📚 更多帮助:
echo    - 详细配置: MANUAL_MCP_SETUP.md
echo    - 测试脚本: python test-mcp-servers.py
echo    - 验证脚本: python verify-mcp-setup.py
echo.
pause
