{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"custom-protocol\"]", "target": 17604022209686676412, "profile": 8731458305071235362, "path": 4942398508502643691, "deps": [[192435385979317305, "rusqlite", false, 14520531742821992369], [246920333930397414, "tauri_plugin_global_shortcut", false, 17382329171050180142], [895066815307484583, "which", false, 11102840268359402488], [1441306149310335789, "tempfile", false, 16671152273431740513], [1582828171158827377, "tauri_plugin_shell", false, 16668719596714217143], [2706460456408817945, "futures", false, 13169558120517196760], [2924422107542798392, "libc", false, 12422987572206778113], [3834743577069889284, "tauri_plugin_dialog", false, 5239105872842557537], [4052408954973158025, "zstd", false, 6856158326659837090], [5986029879202738730, "log", false, 18309695874565327245], [6898646762435821041, "env_logger", false, 5297294908480855558], [7849236192756901113, "tauri_plugin_notification", false, 15684622568948749465], [8218178811151724123, "reqwest", false, 910772910244018617], [8256202458064874477, "dirs", false, 16697948559201963627], [8319709847752024821, "uuid", false, 16617677239534540257], [9451456094439810778, "regex", false, 2432167459552640842], [9538054652646069845, "tokio", false, 17016848415587674367], [9614479274285663593, "serde_yaml", false, 7642149317723909891], [9689903380558560274, "serde", false, 13578168418265350052], [9805105373369294601, "claudia_lib", false, 885135926625124046], [9805105373369294601, "build_script_build", false, 15082208437309005876], [9857275760291862238, "sha2", false, 14588372192480233722], [9897246384292347999, "chrono", false, 5067866995474212709], [10755362358622467486, "tauri", false, 9583120891350849165], [11946729385090170470, "async_trait", false, 278513176963860178], [13077212702700853852, "base64", false, 17250583037651168056], [13625485746686963219, "anyhow", false, 16521415306001099308], [13890802266741835355, "tauri_plugin_fs", false, 16101976588577041406], [13919194856117907555, "tauri_plugin_clipboard_manager", false, 8202237889407874278], [15367738274754116744, "serde_json", false, 15734816997775649336], [15441187897486245138, "tauri_plugin_http", false, 14001340892238164857], [15622660310229662834, "walkdir", false, 17300180680190798912], [17155886227862585100, "glob", false, 16666287207728800018], [17962022290347926134, "tauri_plugin_process", false, 13689480179311366231], [18440762029541581206, "tauri_plugin_updater", false, 3103046779157492985]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\claudia-d1df46e427e7ee26\\dep-bin-claudia", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}