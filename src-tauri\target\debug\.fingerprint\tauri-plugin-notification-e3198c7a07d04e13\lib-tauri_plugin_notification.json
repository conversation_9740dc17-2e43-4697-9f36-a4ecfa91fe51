{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"win7-notifications\", \"windows-version\", \"windows7-compat\"]", "target": 11906320761866078153, "profile": 15657897354478470176, "path": 13708028991866394861, "deps": [[947818755262499932, "notify_rust", false, 17258073781599363146], [3150220818285335163, "url", false, 12833560258718765385], [5986029879202738730, "log", false, 18309695874565327245], [7849236192756901113, "build_script_build", false, 7426109378117500249], [9689903380558560274, "serde", false, 13578168418265350052], [10755362358622467486, "tauri", false, 9583120891350849165], [10806645703491011684, "thiserror", false, 983470577670415442], [12409575957772518135, "time", false, 455319449301019792], [12986574360607194341, "serde_repr", false, 10068094518246514317], [13208667028893622512, "rand", false, 9968581480924046548], [15367738274754116744, "serde_json", false, 15734816997775649336]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-notification-e3198c7a07d04e13\\dep-lib-tauri_plugin_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}