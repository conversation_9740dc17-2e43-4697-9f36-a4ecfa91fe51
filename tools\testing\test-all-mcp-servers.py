#!/usr/bin/env python3
"""
完整MCP服务器测试脚本
测试所有9个MCP服务器是否正确配置并可以运行
"""

import subprocess
import sys
import json
import time
import os
from pathlib import Path

def test_command(name, command, args, timeout=10):
    """测试命令是否可以执行"""
    print(f"🧪 测试 {name}...")
    
    try:
        # 构建完整命令
        full_command = [command] + args
        
        # 启动进程
        process = subprocess.Popen(
            full_command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=Path.cwd()
        )
        
        # 等待一小段时间
        time.sleep(2)
        
        # 检查进程状态
        if process.poll() is None:
            print(f"✅ {name} 启动成功")
            process.terminate()
            process.wait(timeout=5)
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ {name} 启动失败")
            if stderr:
                print(f"   错误: {stderr[:200]}...")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⚠️ {name} 测试超时（可能正常）")
        try:
            process.terminate()
            process.wait(timeout=5)
        except:
            pass
        return True
    except FileNotFoundError:
        print(f"❌ {name} 命令未找到: {command}")
        return False
    except Exception as e:
        print(f"❌ {name} 测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试所有MCP服务器...")
    print("=" * 60)
    
    # 定义所有MCP服务器
    servers = [
        # Node.js服务器
        {
            "name": "📁 filesystem",
            "command": "node",
            "args": ["mcp/servers/src/filesystem/dist/index.js"]
        },
        {
            "name": "🧠 memory", 
            "command": "node",
            "args": ["mcp/servers/src/memory/dist/index.js"]
        },
        {
            "name": "🔄 sequentialthinking",
            "command": "node",
            "args": ["mcp/servers/src/sequentialthinking/dist/index.js"]
        },
        {
            "name": "🔍 everything",
            "command": "node",
            "args": ["mcp/servers/src/everything/dist/index.js"]
        },
        
        # Python服务器
        {
            "name": "🌐 fetch",
            "command": "python",
            "args": ["-m", "mcp_server_fetch"]
        },
        {
            "name": "📦 git",
            "command": "python",
            "args": ["-m", "mcp_server_git"]
        },
        {
            "name": "⏰ time",
            "command": "python",
            "args": ["-m", "mcp_server_time"]
        },
        
        # NPM服务器
        {
            "name": "🎯 context7",
            "command": "node",
            "args": ["node_modules/@upstash/context7-mcp/dist/index.js"]
        },
        {
            "name": "🤖 puppeteer",
            "command": "node",
            "args": ["node_modules/puppeteer-mcp-server/dist/index.js"]
        }
    ]
    
    # 测试每个服务器
    results = {}
    for server in servers:
        success = test_command(
            server["name"],
            server["command"],
            server["args"]
        )
        results[server["name"]] = success
        print()
    
    # 输出结果
    print("=" * 60)
    print("📊 测试结果汇总:")
    print("=" * 60)
    
    success_count = 0
    total_count = len(results)
    
    for server, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{server:25} {status}")
        if success:
            success_count += 1
    
    print("=" * 60)
    print(f"总计: {success_count}/{total_count} 个服务器测试通过")
    
    if success_count == total_count:
        print("🎉 所有MCP服务器都正常工作！")
        print("\n✅ 准备就绪，可以在Claudia中配置MCP服务器")
        print("📖 请参考 FINAL_MCP_CONFIGURATION.md 进行配置")
        return 0
    else:
        print("⚠️ 部分MCP服务器存在问题")
        print("🔧 请检查失败的服务器并重新安装依赖")
        return 1

if __name__ == "__main__":
    sys.exit(main())
