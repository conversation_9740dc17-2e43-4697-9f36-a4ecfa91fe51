# 🔌 Claudia MCP服务器配置指南

## 📋 已安装的MCP服务器

以下MCP服务器已成功安装并可在Claudia中使用：

### 1. 📁 **Filesystem MCP Server**
- **功能**: 文件系统操作（读取、写入、创建、删除文件和目录）
- **类型**: Node.js/TypeScript
- **命令**: `node mcp/servers/src/filesystem/index.ts`
- **工作目录**: `C:\Users\<USER>\Desktop\claudia`

### 2. 🔍 **Everything MCP Server**  
- **功能**: 快速文件搜索（基于Everything搜索引擎）
- **类型**: Node.js/TypeScript
- **命令**: `node mcp/servers/src/everything/index.ts`
- **工作目录**: `C:\Users\<USER>\Desktop\claudia`

### 3. 🧠 **Memory MCP Server**
- **功能**: 内存管理和数据持久化
- **类型**: Node.js/TypeScript  
- **命令**: `node mcp/servers/src/memory/index.ts`
- **工作目录**: `C:\Users\<USER>\Desktop\claudia`

### 4. 🔄 **Sequential Thinking MCP Server**
- **功能**: 顺序思维和逻辑推理
- **类型**: Node.js/TypeScript
- **命令**: `node mcp/servers/src/sequentialthinking/index.ts`
- **工作目录**: `C:\Users\<USER>\Desktop\claudia`

### 5. 🌐 **Fetch MCP Server**
- **功能**: HTTP请求和网页抓取
- **类型**: Python
- **命令**: `python -m mcp_server_fetch`
- **工作目录**: `C:\Users\<USER>\Desktop\claudia`

### 6. 📦 **Git MCP Server**
- **功能**: Git版本控制操作
- **类型**: Python
- **命令**: `python -m mcp_server_git`
- **工作目录**: `C:\Users\<USER>\Desktop\claudia`

### 7. ⏰ **Time MCP Server**
- **功能**: 时间和日期操作
- **类型**: Python
- **命令**: `python -m mcp_server_time`
- **工作目录**: `C:\Users\<USER>\Desktop\claudia`

## 🚀 在Claudia中添加MCP服务器

### 步骤1: 打开Claudia设置
1. 在浏览器中访问 `http://localhost:1420`
2. 点击右上角的 **Settings** (⚙️)
3. 选择 **MCP** 标签页

### 步骤2: 添加服务器
对于每个服务器，点击 **Add Server** 并填写以下信息：

#### Filesystem Server
- **Name**: `filesystem`
- **Command**: `node`
- **Args**: `["mcp/servers/src/filesystem/dist/index.js"]`
- **Env**: `{}`

#### Everything Server
- **Name**: `everything`
- **Command**: `node`
- **Args**: `["mcp/servers/src/everything/dist/index.js"]`
- **Env**: `{}`

#### Memory Server
- **Name**: `memory`
- **Command**: `node`
- **Args**: `["mcp/servers/src/memory/dist/index.js"]`
- **Env**: `{}`

#### Sequential Thinking Server
- **Name**: `sequentialthinking`
- **Command**: `node`
- **Args**: `["mcp/servers/src/sequentialthinking/dist/index.js"]`
- **Env**: `{}`

#### Fetch Server
- **Name**: `fetch`
- **Command**: `python`
- **Args**: `["-m", "mcp_server_fetch"]`
- **Env**: `{}`

#### Git Server
- **Name**: `git`
- **Command**: `python`
- **Args**: `["-m", "mcp_server_git"]`
- **Env**: `{}`

#### Time Server
- **Name**: `time`
- **Command**: `python`
- **Args**: `["-m", "mcp_server_time"]`
- **Env**: `{}`

## ✅ 验证安装

添加服务器后，你可以：
1. 点击每个服务器旁边的 **Test Connection** 按钮
2. 检查服务器状态是否显示为 **Connected**
3. 在Claude Code中使用相应的MCP功能

## 🔧 故障排除

如果遇到问题：
1. 确保所有依赖都已正确安装
2. 检查Python和Node.js路径是否正确
3. 查看Claudia的日志输出
4. 重启Claudia应用

## 📚 功能说明

安装完成后，你将能够：
- 📁 通过filesystem服务器管理文件和目录
- 🔍 使用everything服务器快速搜索文件
- 🧠 利用memory服务器进行数据持久化
- 🔄 通过sequentialthinking服务器进行逻辑推理
- 🌐 使用fetch服务器进行网络请求
- 📦 通过git服务器管理版本控制
- ⏰ 利用time服务器处理时间相关操作
