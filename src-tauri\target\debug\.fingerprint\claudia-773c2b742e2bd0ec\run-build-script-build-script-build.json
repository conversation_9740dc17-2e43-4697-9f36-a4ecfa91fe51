{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9805105373369294601, "build_script_build", false, 2457073759030188259], [10755362358622467486, "build_script_build", false, 6797351182006103954], [13919194856117907555, "build_script_build", false, 7112538789560893393], [3834743577069889284, "build_script_build", false, 6056464619948742838], [13890802266741835355, "build_script_build", false, 678354854607061739], [246920333930397414, "build_script_build", false, 15716421967926694360], [15441187897486245138, "build_script_build", false, 4651140424611054868], [7849236192756901113, "build_script_build", false, 7426109378117500249], [17962022290347926134, "build_script_build", false, 9692643881684995828], [1582828171158827377, "build_script_build", false, 15175780074608092682], [18440762029541581206, "build_script_build", false, 4040584821916286445]], "local": [{"RerunIfChanged": {"output": "debug\\build\\claudia-773c2b742e2bd0ec\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}