{"$schema": "https://schema.tauri.app/config/2", "productName": "<PERSON>", "version": "0.1.0", "identifier": "claudia.asterisk.so", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "<PERSON>", "width": 800, "height": 600}], "security": {"csp": "default-src 'self'; img-src 'self' asset: https://asset.localhost blob: data:; style-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-eval' https://app.posthog.com https://*.posthog.com https://*.i.posthog.com https://*.assets.i.posthog.com; connect-src 'self' ipc: https://ipc.localhost https://app.posthog.com https://*.posthog.com https://*.i.posthog.com", "assetProtocol": {"enable": true, "scope": ["**"]}}}, "plugins": {"fs": {"scope": ["$HOME/**"], "allow": ["readFile", "writeFile", "readDir", "copyFile", "createDir", "removeDir", "removeFile", "renameFile", "exists"]}, "shell": {"open": true}}, "bundle": {"active": true, "targets": ["deb", "rpm", "appimage", "app", "dmg"], "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns"], "resources": [], "externalBin": [], "copyright": "© 2025 Asterisk. All rights reserved.", "category": "DeveloperTool", "shortDescription": "GUI app and Toolkit for Claude Code", "longDescription": "Claudia is a comprehensive GUI application and toolkit for working with Claude Code, providing an intuitive interface for AI-assisted development.", "linux": {"appimage": {"bundleMediaFramework": true}, "deb": {"depends": ["libwebkit2gtk-4.1-0", "libgtk-3-0"]}}, "macOS": {"frameworks": [], "minimumSystemVersion": "10.15", "exceptionDomain": "", "signingIdentity": null, "providerShortName": null, "entitlements": "entitlements.plist"}, "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": ""}}}