{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 17231900515118247201, "deps": [[376837177317575824, "softbuffer", false, 17153127995188101498], [442785307232013896, "tauri_runtime", false, 12184482301370920892], [3150220818285335163, "url", false, 12833560258718765385], [3722963349756955755, "once_cell", false, 14521976164538839542], [4143744114649553716, "raw_window_handle", false, 14178324387943116473], [5986029879202738730, "log", false, 18309695874565327245], [7752760652095876438, "build_script_build", false, 482409782188166509], [8539587424388551196, "webview2_com", false, 16239625063982462944], [9010263965687315507, "http", false, 15629939712500996649], [11050281405049894993, "tauri_utils", false, 13949741672004295777], [13116089016666501665, "windows", false, 15514005909746006525], [13223659721939363523, "tao", false, 11099183893950664123], [14794439852947137341, "wry", false, 9068835479991520318]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-8ca8fdaf81b46c18\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}