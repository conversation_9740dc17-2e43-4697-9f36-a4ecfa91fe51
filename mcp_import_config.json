{"mcpServers": {"filesystem": {"command": "node", "args": ["C:\\Users\\<USER>\\Desktop\\claudia\\mcp\\servers\\src\\filesystem\\dist\\index.js"], "env": {}}, "memory": {"command": "node", "args": ["C:\\Users\\<USER>\\Desktop\\claudia\\mcp\\servers\\src\\memory\\dist\\index.js"], "env": {}}, "sequentialthinking": {"command": "node", "args": ["C:\\Users\\<USER>\\Desktop\\claudia\\mcp\\servers\\src\\sequentialthinking\\dist\\index.js"], "env": {}}, "everything": {"command": "node", "args": ["C:\\Users\\<USER>\\Desktop\\claudia\\mcp\\servers\\src\\everything\\dist\\index.js"], "env": {}}, "fetch": {"command": "python", "args": ["-m", "mcp_server_fetch"], "env": {}}, "git": {"command": "python", "args": ["-m", "mcp_server_git"], "env": {}}, "time": {"command": "python", "args": ["-m", "mcp_server_time"], "env": {}}, "context7": {"command": "node", "args": ["C:\\Users\\<USER>\\Desktop\\claudia\\node_modules\\@upstash\\context7-mcp\\dist\\index.js"], "env": {}}, "puppeteer": {"command": "node", "args": ["C:\\Users\\<USER>\\Desktop\\claudia\\node_modules\\puppeteer-mcp-server\\dist\\index.js"], "env": {}}}}