@echo off
title Claudia项目启动器
echo.
echo ========================================
echo    Claudia项目启动器
echo ========================================
echo.
echo 选择操作:
echo 1. 启动Claudia
echo 2. 配置MCP服务器
echo 3. 测试MCP服务器
echo 4. 查看项目结构
echo 5. 退出
echo.
set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" (
    echo 启动Claudia...
    call scripts\setup\start-claudia.bat
) else if "%choice%"=="2" (
    echo 打开MCP配置助手...
    call tools\import\import-helper-en.bat
) else if "%choice%"=="3" (
    echo 运行MCP测试...
    python tools\testing\test-all-mcp-servers.py
    pause
) else if "%choice%"=="4" (
    echo 打开项目结构文档...
    start PROJECT_STRUCTURE.md
) else if "%choice%"=="5" (
    exit
) else (
    echo 无效选择，请重试
    pause
    goto :eof
)
