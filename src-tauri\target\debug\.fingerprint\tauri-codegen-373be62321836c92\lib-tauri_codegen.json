{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 8985965904563823836, "deps": [[3060637413840920116, "proc_macro2", false, 5373005460213438867], [3150220818285335163, "url", false, 3744897104065425490], [4899080583175475170, "semver", false, 6587108540636782383], [7170110829644101142, "json_patch", false, 15473634152578215941], [7392050791754369441, "ico", false, 15473060213138448463], [8319709847752024821, "uuid", false, 7428505576557014790], [9689903380558560274, "serde", false, 8619023586440547223], [9857275760291862238, "sha2", false, 14588372192480233722], [10806645703491011684, "thiserror", false, 983470577670415442], [11050281405049894993, "tauri_utils", false, 8557585670532656720], [12687914511023397207, "png", false, 5880946256663243828], [13077212702700853852, "base64", false, 17250583037651168056], [14132538657330703225, "brotli", false, 17325694709048316288], [15367738274754116744, "serde_json", false, 4475620309241193984], [15622660310229662834, "walkdir", false, 4450029676203649764], [17990358020177143287, "quote", false, 9227116837396871820], [18149961000318489080, "syn", false, 14812274515719501434]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-373be62321836c92\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}