{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"http-range\", \"image\", \"image-png\", \"protocol-asset\", \"tauri-runtime-wry\", \"tray-icon\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 2484673656501667652, "deps": [[40386456601120721, "percent_encoding", false, 4129156245155979692], [442785307232013896, "tauri_runtime", false, 12184482301370920892], [1200537532907108615, "url<PERSON><PERSON>n", false, 127465540148564513], [3150220818285335163, "url", false, 12833560258718765385], [4143744114649553716, "raw_window_handle", false, 14178324387943116473], [4341921533227644514, "muda", false, 2902937655835697437], [4919829919303820331, "serialize_to_javascript", false, 1673163225834467570], [5986029879202738730, "log", false, 18309695874565327245], [7752760652095876438, "tauri_runtime_wry", false, 9408867029487446020], [8351317599104215083, "tray_icon", false, 17875660973578302757], [8539587424388551196, "webview2_com", false, 16239625063982462944], [8866577183823226611, "http_range", false, 17802822684938098769], [************7315507, "http", false, 15629939712500996649], [9228235415475680086, "tauri_macros", false, 8906287664980141268], [9538054652646069845, "tokio", false, 17016848415587674367], [9689903380558560274, "serde", false, 13578168418265350052], [9920160576179037441, "getrandom", false, 3613234935103388590], [10229185211513642314, "mime", false, 12149313404929737718], [10629569228670356391, "futures_util", false, 10517449367561583863], [10755362358622467486, "build_script_build", false, 6797351182006103954], [10806645703491011684, "thiserror", false, 983470577670415442], [11050281405049894993, "tauri_utils", false, 13949741672004295777], [11989259058781683633, "dunce", false, 11426863789755588728], [12565293087094287914, "window_vibrancy", false, 13872613014281309858], [12986574360607194341, "serde_repr", false, 10068094518246514317], [13028763805764736075, "image", false, 11532192426206570095], [13077543566650298139, "heck", false, 12227043807642275620], [13116089016666501665, "windows", false, 15514005909746006525], [13625485746686963219, "anyhow", false, 16521415306001099308], [15367738274754116744, "serde_json", false, 15734816997775649336], [16928111194414003569, "dirs", false, 6198109939901503216], [17155886227862585100, "glob", false, 16666287207728800018]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-772dbd543e3c55e4\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}