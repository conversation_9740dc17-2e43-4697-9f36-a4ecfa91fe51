/**
 * 浏览器中执行的MCP服务器导入脚本
 * 在Claudia页面的控制台中运行此脚本来自动导入MCP服务器
 */

// MCP服务器配置
const mcpServers = {
  filesystem: {
    command: "node",
    args: ["C:\\Users\\<USER>\\Desktop\\claudia\\mcp\\servers\\src\\filesystem\\dist\\index.js"],
    env: {}
  },
  memory: {
    command: "node",
    args: ["C:\\Users\\<USER>\\Desktop\\claudia\\mcp\\servers\\src\\memory\\dist\\index.js"],
    env: {}
  },
  sequentialthinking: {
    command: "node",
    args: ["C:\\Users\\<USER>\\Desktop\\claudia\\mcp\\servers\\src\\sequentialthinking\\dist\\index.js"],
    env: {}
  },
  everything: {
    command: "node",
    args: ["C:\\Users\\<USER>\\Desktop\\claudia\\mcp\\servers\\src\\everything\\dist\\index.js"],
    env: {}
  },
  fetch: {
    command: "python",
    args: ["-m", "mcp_server_fetch"],
    env: {}
  },
  git: {
    command: "python",
    args: ["-m", "mcp_server_git"],
    env: {}
  },
  time: {
    command: "python",
    args: ["-m", "mcp_server_time"],
    env: {}
  },
  context7: {
    command: "node",
    args: ["C:\\Users\\<USER>\\Desktop\\claudia\\node_modules\\@upstash\\context7-mcp\\dist\\index.js"],
    env: {}
  },
  puppeteer: {
    command: "node",
    args: ["C:\\Users\\<USER>\\Desktop\\claudia\\node_modules\\puppeteer-mcp-server\\dist\\index.js"],
    env: {}
  }
};

// 导入函数
async function importMCPServers() {
  console.log('🚀 开始导入MCP服务器...');
  
  // 检查是否在Claudia页面
  if (!window.location.href.includes('localhost:1420')) {
    console.error('❌ 请在Claudia页面 (http://localhost:1420) 中运行此脚本');
    return;
  }
  
  // 检查Tauri API是否可用
  if (!window.__TAURI__) {
    console.error('❌ Tauri API不可用，请确保在Claudia应用中运行');
    return;
  }
  
  const { invoke } = window.__TAURI__.tauri;
  
  let successCount = 0;
  let totalCount = Object.keys(mcpServers).length;
  
  console.log(`📋 准备导入 ${totalCount} 个MCP服务器`);
  
  for (const [name, config] of Object.entries(mcpServers)) {
    try {
      console.log(`🔧 添加服务器: ${name}`);
      
      const result = await invoke('mcp_add', {
        name: name,
        transport: 'stdio',
        command: config.command,
        args: config.args,
        env: config.env,
        url: null,
        scope: 'project'
      });
      
      if (result.success) {
        console.log(`✅ ${name} 添加成功`);
        successCount++;
      } else {
        console.error(`❌ ${name} 添加失败: ${result.message}`);
      }
      
    } catch (error) {
      console.error(`❌ ${name} 添加异常:`, error);
    }
    
    // 添加延迟避免API调用过快
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('=' .repeat(50));
  console.log(`📊 导入结果: ${successCount}/${totalCount} 成功`);
  
  if (successCount === totalCount) {
    console.log('🎉 所有MCP服务器导入成功！');
    console.log('💡 请刷新页面查看MCP服务器列表');
  } else {
    console.log('⚠️ 部分服务器导入失败，请检查错误信息');
  }
  
  return { successCount, totalCount };
}

// 导出函数供控制台使用
window.importMCPServers = importMCPServers;

console.log('📖 MCP服务器导入脚本已加载');
console.log('💡 运行 importMCPServers() 开始导入');
console.log('🔗 或者直接运行以下命令:');
console.log('   importMCPServers()');

// 如果需要立即执行，取消注释下面这行
// importMCPServers();
