{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 15657897354478470176, "path": 2911265107896841603, "deps": [[500211409582349667, "shared_child", false, 6932922454674939067], [1582828171158827377, "build_script_build", false, 15175780074608092682], [5986029879202738730, "log", false, 18309695874565327245], [9451456094439810778, "regex", false, 2432167459552640842], [9538054652646069845, "tokio", false, 17016848415587674367], [9689903380558560274, "serde", false, 13578168418265350052], [10755362358622467486, "tauri", false, 9583120891350849165], [10806645703491011684, "thiserror", false, 983470577670415442], [11337703028400419576, "os_pipe", false, 16193666891672786208], [14564311161534545801, "encoding_rs", false, 4174922264792711530], [15367738274754116744, "serde_json", false, 15734816997775649336], [16192041687293812804, "open", false, 16371171263966997954]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-shell-e4517232a259535c\\dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}