# 🎉 Claudia AI Assistant with MCP Servers

一个功能强大的AI助手，集成了9个MCP (Model Context Protocol) 服务器，提供文件操作、搜索、内存管理、浏览器自动化等功能。

## 🚀 快速开始

### 1. 启动应用
```bash
# 运行主启动器
launch.bat

# 或直接启动Claudia
scripts/setup/start-claudia.bat
```

### 2. 配置MCP服务器
```bash
# 使用导入助手
tools/import/import-helper-en.bat

# 或手动配置，参考文档
docs/mcp/FINAL_MCP_CONFIGURATION.md
```

### 3. 测试功能
```bash
# 测试所有MCP服务器
python tools/testing/test-all-mcp-servers.py
```

## 📦 MCP服务器功能

| 服务器 | 功能 | 状态 |
|--------|------|------|
| 📁 **Filesystem** | 文件系统操作 | ✅ 就绪 |
| 🧠 **Memory** | 内存管理 | ✅ 就绪 |
| 🔄 **Sequential Thinking** | 顺序思维 | ✅ 就绪 |
| 🔍 **Everything** | 文件搜索 | ✅ 就绪 |
| 🌐 **Fetch** | HTTP请求 | ✅ 就绪 |
| 📦 **Git** | Git操作 | ✅ 就绪 |
| ⏰ **Time** | 时间工具 | ✅ 就绪 |
| 🎯 **Context7** | 代码上下文 | ✅ 就绪 |
| 🤖 **Puppeteer** | 浏览器自动化 | ✅ 就绪 |

## 📁 项目结构

```
claudia/
├── 📖 docs/              # 文档
│   ├── mcp/              # MCP相关文档
│   └── setup/            # 设置文档
├── ⚙️ configs/           # 配置文件
│   └── mcp/              # MCP配置
├── 🔧 scripts/           # 脚本
│   ├── setup/            # 启动脚本
│   └── mcp/              # MCP脚本
├── 🛠️ tools/             # 工具
│   ├── import/           # 导入工具
│   └── testing/          # 测试工具
├── 📦 mcp/               # MCP服务器源码
├── 💻 src/               # 应用源码
├── 🏗️ src-tauri/         # Tauri后端
└── 🚀 launch.bat         # 主启动器
```

## 📚 重要文档

- **[项目结构说明](PROJECT_STRUCTURE.md)** - 详细的目录结构
- **[MCP配置指南](docs/mcp/FINAL_MCP_CONFIGURATION.md)** - 完整配置指南
- **[导入步骤](docs/mcp/STEP_BY_STEP_IMPORT.md)** - 详细导入步骤
- **[功能总结](docs/mcp/IMPORT_READY_SUMMARY.md)** - 功能概览

## 🔧 开发

### 启动开发环境
```bash
npm run tauri dev
```

### 构建应用
```bash
npm run tauri build
```

### 测试MCP服务器
```bash
python tools/testing/test-all-mcp-servers.py
```

## 🆘 故障排除

1. **MCP服务器无法连接**
   - 检查依赖是否安装: `python tools/testing/verify-mcp-setup.py`
   - 重新配置服务器: `scripts/setup/configure-mcp-servers.bat`

2. **应用无法启动**
   - 检查Node.js和Rust环境
   - 重新安装依赖: `npm install`

3. **导入失败**
   - 使用手动导入: 参考 `docs/mcp/STEP_BY_STEP_IMPORT.md`
   - 检查配置文件: `configs/mcp/`

## 📄 许可证

本项目基于 MIT 许可证开源。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

**享受强大的AI助手体验！** 🚀
