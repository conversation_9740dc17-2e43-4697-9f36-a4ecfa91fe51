#!/usr/bin/env node
/**
 * Claudia MCP服务器批量导入脚本
 * 通过Tauri API直接导入MCP服务器配置
 */

import fs from 'fs';
import path from 'path';
import { exec } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// MCP服务器配置
const mcpServers = {
  filesystem: {
    command: "node",
    args: [path.resolve(__dirname, "mcp/servers/src/filesystem/dist/index.js")],
    env: {}
  },
  memory: {
    command: "node", 
    args: [path.resolve(__dirname, "mcp/servers/src/memory/dist/index.js")],
    env: {}
  },
  sequentialthinking: {
    command: "node",
    args: [path.resolve(__dirname, "mcp/servers/src/sequentialthinking/dist/index.js")],
    env: {}
  },
  everything: {
    command: "node",
    args: [path.resolve(__dirname, "mcp/servers/src/everything/dist/index.js")],
    env: {}
  },
  fetch: {
    command: "python",
    args: ["-m", "mcp_server_fetch"],
    env: {}
  },
  git: {
    command: "python", 
    args: ["-m", "mcp_server_git"],
    env: {}
  },
  time: {
    command: "python",
    args: ["-m", "mcp_server_time"],
    env: {}
  }
};

async function importToClaudia() {
  console.log('🚀 开始批量导入MCP服务器到Claudia...');
  console.log('=' .repeat(60));
  
  // 检查Claudia是否运行
  try {
    const response = await fetch('http://localhost:1420');
    if (!response.ok) {
      throw new Error('Claudia not responding');
    }
    console.log('✅ Claudia应用正在运行');
  } catch (error) {
    console.log('❌ Claudia应用未运行，请先启动Claudia');
    console.log('   运行命令: npm run tauri dev');
    process.exit(1);
  }
  
  // 创建.mcp.json配置文件
  const mcpConfig = {
    mcpServers: mcpServers
  };
  
  const configPath = path.join(__dirname, '.mcp.json');
  fs.writeFileSync(configPath, JSON.stringify(mcpConfig, null, 2));
  console.log('✅ 已创建 .mcp.json 配置文件');
  
  console.log('\n📋 准备导入的MCP服务器:');
  Object.keys(mcpServers).forEach((name, index) => {
    const server = mcpServers[name];
    console.log(`${index + 1}. 📦 ${name}`);
    console.log(`   命令: ${server.command}`);
    console.log(`   参数: ${server.args.join(' ')}`);
  });
  
  console.log('\n🎯 下一步操作:');
  console.log('1. 打开 Claudia: http://localhost:1420');
  console.log('2. 在对话框底部点击 MCP 选项');
  console.log('3. 选择 Import/Export 标签页');
  console.log('4. 导入 .mcp.json 配置文件');
  console.log('\n或者手动添加每个服务器（参考上面的配置信息）');
  
  // 尝试打开浏览器
  exec('start http://localhost:1420', (error) => {
    if (error) {
      console.log('\n🌐 请手动打开: http://localhost:1420');
    } else {
      console.log('\n🌐 正在打开Claudia...');
    }
  });
}

// 检查Context7和Puppeteer
async function checkAdditionalServers() {
  console.log('\n🔍 检查额外的MCP服务器...');
  
  // 检查是否有Context7
  const context7Paths = [
    'mcp/servers/src/context7',
    'node_modules/@context7/mcp-server',
    'mcp/servers/src/context'
  ];
  
  let context7Found = false;
  for (const p of context7Paths) {
    if (fs.existsSync(p)) {
      console.log(`✅ 找到 Context7: ${p}`);
      context7Found = true;
      break;
    }
  }
  
  if (!context7Found) {
    console.log('⚠️ 未找到 Context7 MCP服务器');
    console.log('   可能需要单独安装: npm install @context7/mcp-server');
  }
  
  // 检查是否有Puppeteer
  const puppeteerPaths = [
    'mcp/servers/src/puppeteer',
    'mcp/servers/src/browser-tools-mcp',
    'node_modules/@puppeteer/mcp-server'
  ];
  
  let puppeteerFound = false;
  for (const p of puppeteerPaths) {
    if (fs.existsSync(p)) {
      console.log(`✅ 找到 Puppeteer: ${p}`);
      puppeteerFound = true;
      break;
    }
  }
  
  if (!puppeteerFound) {
    console.log('⚠️ 未找到 Puppeteer MCP服务器');
    console.log('   可能需要单独安装: npm install @puppeteer/mcp-server');
  }
  
  return { context7Found, puppeteerFound };
}

async function main() {
  try {
    await checkAdditionalServers();
    await importToClaudia();
  } catch (error) {
    console.error('❌ 导入失败:', error.message);
    process.exit(1);
  }
}

// 直接运行主函数
main();
