@echo off
title Claudia - Restart & Clean
echo ========================================
echo      Claudia Restart & Clean Tool
echo ========================================
echo.
echo This script will:
echo 1. Kill any running Claudia processes
echo 2. Clean build cache
echo 3. Restart in normal mode
echo.
echo Press any key to continue or Ctrl+C to cancel...
pause >nul
echo.

cd /d "%~dp0"

echo [1/4] Killing existing processes...
taskkill /f /im claudia.exe 2>nul
taskkill /f /im node.exe /fi "WINDOWTITLE eq *claudia*" 2>nul
echo Done.

echo [2/4] Cleaning Rust build cache...
cd src-tauri
cargo clean
cd ..
echo Done.

echo [3/4] Cleaning Node modules cache...
npm run dev -- --force 2>nul
echo Done.

echo [4/4] Starting Claudia...
echo ========================================
echo    <PERSON> is starting fresh!
echo ========================================
echo.

npm run tauri dev

pause
