@echo off
title Configure MCP Servers for Claudia
echo ========================================
echo    Configuring MCP Servers for Claudia
echo ========================================
echo.

cd /d "%~dp0"

echo [1/7] Installing dependencies for filesystem MCP server...
cd mcp\servers\src\filesystem
call npm install
cd ..\..\..\..

echo [2/7] Installing dependencies for everything MCP server...
cd mcp\servers\src\everything
call npm install
cd ..\..\..\..

echo [3/7] Installing dependencies for memory MCP server...
cd mcp\servers\src\memory
call npm install
cd ..\..\..\..

echo [4/7] Installing dependencies for sequentialthinking MCP server...
cd mcp\servers\src\sequentialthinking
call npm install
cd ..\..\..\..

echo [5/7] Installing Python dependencies for fetch MCP server...
cd mcp\servers\src\fetch
pip install -e .
cd ..\..\..\..

echo [6/7] Installing Python dependencies for git MCP server...
cd mcp\servers\src\git
pip install -e .
cd ..\..\..\..

echo [7/7] Installing Python dependencies for time MCP server...
cd mcp\servers\src\time
pip install -e .
cd ..\..\..\..

echo.
echo ========================================
echo    MCP Servers Configuration Complete!
echo ========================================
echo.
echo Available MCP Servers:
echo - filesystem: File system operations
echo - everything: Search functionality  
echo - memory: Memory management
echo - sequentialthinking: Sequential thinking
echo - fetch: HTTP requests
echo - git: Git operations
echo - time: Time utilities
echo.
echo Next steps:
echo 1. Open Claudia at http://localhost:1420
echo 2. Go to Settings ^> MCP
echo 3. Add the configured servers
echo.
pause
