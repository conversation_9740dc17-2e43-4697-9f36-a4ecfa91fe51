@echo off
title Claudia with MCP Servers
echo ========================================
echo    Starting Claudia with MCP Support
echo ========================================
echo.

cd /d "%~dp0"

echo 🚀 Starting Claudia application...
echo.
echo Available MCP Servers:
echo - 📁 filesystem: File system operations
echo - 🔍 everything: Fast file search
echo - 🧠 memory: Memory management
echo - 🔄 sequentialthinking: Sequential thinking
echo - 🌐 fetch: HTTP requests
echo - 📦 git: Git operations
echo - ⏰ time: Time utilities
echo.
echo 🌐 Claudia will be available at: http://localhost:1420
echo 📖 Configuration guide: MCP_SERVERS_CONFIG.md
echo.

REM Start Claudia in development mode
start "Claudia Dev Server" cmd /k "npm run tauri dev"

REM Wait a moment for the server to start
timeout /t 5 /nobreak >nul

REM Open browser
echo 🌐 Opening Claudia in your default browser...
start http://localhost:1420

echo.
echo ✅ Claudia is starting up!
echo.
echo Next steps:
echo 1. Wait for <PERSON> to fully load
echo 2. Go to Settings ^> MCP in Claudia
echo 3. Add the MCP servers using the configuration guide
echo.
echo Press any key to exit this window...
pause >nul
