C:\Users\<USER>\Desktop\claudia\src-tauri\target\debug\deps\claudia_lib.d: src\lib.rs src\checkpoint\mod.rs src\checkpoint\manager.rs src\checkpoint\state.rs src\checkpoint\storage.rs src\claude_binary.rs src\commands\mod.rs src\commands\agents.rs src\commands\claude.rs src\commands\mcp.rs src\commands\usage.rs src\commands\storage.rs src\commands\slash_commands.rs src\commands\proxy.rs src\process\mod.rs src\process\registry.rs C:\Users\<USER>\Desktop\claudia\src-tauri\target\debug\build\claudia-773c2b742e2bd0ec\out/02acddb7937ef0912382c373ed55f2882f3124c2a6a6c8f462e0cfc098fecbb5

C:\Users\<USER>\Desktop\claudia\src-tauri\target\debug\deps\libclaudia_lib.rlib: src\lib.rs src\checkpoint\mod.rs src\checkpoint\manager.rs src\checkpoint\state.rs src\checkpoint\storage.rs src\claude_binary.rs src\commands\mod.rs src\commands\agents.rs src\commands\claude.rs src\commands\mcp.rs src\commands\usage.rs src\commands\storage.rs src\commands\slash_commands.rs src\commands\proxy.rs src\process\mod.rs src\process\registry.rs C:\Users\<USER>\Desktop\claudia\src-tauri\target\debug\build\claudia-773c2b742e2bd0ec\out/02acddb7937ef0912382c373ed55f2882f3124c2a6a6c8f462e0cfc098fecbb5

C:\Users\<USER>\Desktop\claudia\src-tauri\target\debug\deps\claudia_lib.dll: src\lib.rs src\checkpoint\mod.rs src\checkpoint\manager.rs src\checkpoint\state.rs src\checkpoint\storage.rs src\claude_binary.rs src\commands\mod.rs src\commands\agents.rs src\commands\claude.rs src\commands\mcp.rs src\commands\usage.rs src\commands\storage.rs src\commands\slash_commands.rs src\commands\proxy.rs src\process\mod.rs src\process\registry.rs C:\Users\<USER>\Desktop\claudia\src-tauri\target\debug\build\claudia-773c2b742e2bd0ec\out/02acddb7937ef0912382c373ed55f2882f3124c2a6a6c8f462e0cfc098fecbb5

C:\Users\<USER>\Desktop\claudia\src-tauri\target\debug\deps\claudia_lib.lib: src\lib.rs src\checkpoint\mod.rs src\checkpoint\manager.rs src\checkpoint\state.rs src\checkpoint\storage.rs src\claude_binary.rs src\commands\mod.rs src\commands\agents.rs src\commands\claude.rs src\commands\mcp.rs src\commands\usage.rs src\commands\storage.rs src\commands\slash_commands.rs src\commands\proxy.rs src\process\mod.rs src\process\registry.rs C:\Users\<USER>\Desktop\claudia\src-tauri\target\debug\build\claudia-773c2b742e2bd0ec\out/02acddb7937ef0912382c373ed55f2882f3124c2a6a6c8f462e0cfc098fecbb5

src\lib.rs:
src\checkpoint\mod.rs:
src\checkpoint\manager.rs:
src\checkpoint\state.rs:
src\checkpoint\storage.rs:
src\claude_binary.rs:
src\commands\mod.rs:
src\commands\agents.rs:
src\commands\claude.rs:
src\commands\mcp.rs:
src\commands\usage.rs:
src\commands\storage.rs:
src\commands\slash_commands.rs:
src\commands\proxy.rs:
src\process\mod.rs:
src\process\registry.rs:
C:\Users\<USER>\Desktop\claudia\src-tauri\target\debug\build\claudia-773c2b742e2bd0ec\out/02acddb7937ef0912382c373ed55f2882f3124c2a6a6c8f462e0cfc098fecbb5:

# env-dep:CARGO_PKG_AUTHORS=mufeedvh:123vviekr
# env-dep:CARGO_PKG_DESCRIPTION=GUI app and Toolkit for Claude Code
# env-dep:CARGO_PKG_NAME=claudia
# env-dep:OUT_DIR=C:\\Users\\<USER>\\Desktop\\claudia\\src-tauri\\target\\debug\\build\\claudia-773c2b742e2bd0ec\\out
