{"rustc": 1842507548689473721, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"macos-system-configuration\", \"native-tls\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"stream\", \"system-proxy\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 15302557990823967831, "path": 304012281195555478, "deps": [[40386456601120721, "percent_encoding", false, 4129156245155979692], [418947936956741439, "h2", false, 6008311843255926859], [778154619793643451, "hyper_util", false, 13485738430088958708], [784494742817713399, "tower_service", false, 11660862420847627360], [1288403060204016458, "tokio_util", false, 6846598795172241700], [1788832197870803419, "hyper_rustls", false, 1934149965501382975], [1906322745568073236, "pin_project_lite", false, 8963179910468966138], [2054153378684941554, "tower_http", false, 16090909276964337260], [2517136641825875337, "sync_wrapper", false, 11531262691852108096], [2883436298747778685, "rustls_pki_types", false, 7785388080773837983], [3150220818285335163, "url", false, 12833560258718765385], [5695049318159433696, "tower", false, 5100352068092448737], [5907992341687085091, "webpki_roots", false, 1751158182921860398], [5986029879202738730, "log", false, 18309695874565327245], [7620660491849607393, "futures_core", false, 5897199496535336083], [8298091525883606470, "cookie_store", false, 16569849201009642146], [9010263965687315507, "http", false, 15629939712500996649], [9538054652646069845, "tokio", false, 17016848415587674367], [9689903380558560274, "serde", false, 13578168418265350052], [10229185211513642314, "mime", false, 12149313404929737718], [10629569228670356391, "futures_util", false, 10517449367561583863], [11895591994124935963, "tokio_rustls", false, 17875599491645615811], [11957360342995674422, "hyper", false, 1348901772608261453], [12186126227181294540, "tokio_native_tls", false, 7057937367123085549], [13077212702700853852, "base64", false, 17250583037651168056], [14084095096285906100, "http_body", false, 13962944359390943063], [14564311161534545801, "encoding_rs", false, 4174922264792711530], [15367738274754116744, "serde_json", false, 15734816997775649336], [16066129441945555748, "bytes", false, 2626033548966229242], [16400140949089969347, "rustls", false, 13713184312281441129], [16542808166767769916, "serde_urlencoded", false, 10101904375171225884], [16727543399706004146, "cookie_crate", false, 15901587362592152082], [16785601910559813697, "native_tls_crate", false, 14046618475231762705], [16900715236047033623, "http_body_util", false, 10967508203520950487], [18273243456331255970, "hyper_tls", false, 14860460079645137888]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-c4a8d09418f12fb8\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}