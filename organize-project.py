#!/usr/bin/env python3
"""
Claudia项目文件整理脚本
将散落的文件按功能分类整理到合适的目录中
"""

import os
import shutil
import json
from pathlib import Path

def create_directory_structure():
    """创建标准的目录结构"""
    directories = [
        'docs',
        'docs/mcp',
        'docs/setup',
        'docs/guides',
        'scripts',
        'scripts/mcp',
        'scripts/setup',
        'configs',
        'configs/mcp',
        'tools',
        'tools/testing',
        'tools/import',
        'backup',
        'backup/old-configs'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建目录: {directory}")

def organize_files():
    """整理文件到相应目录"""
    
    # 文件移动映射
    file_moves = {
        # MCP文档
        'docs/mcp/': [
            'FINAL_MCP_CONFIGURATION.md',
            'MCP_SERVERS_CONFIG.md',
            'MANUAL_MCP_SETUP.md',
            'MCP_BATCH_IMPORT_SUCCESS.md',
            'MCP_DEPLOYMENT_COMPLETE.md',
            'MCP_IMPORT_SUCCESS_REPORT.md',
            'STEP_BY_STEP_IMPORT.md',
            'IMPORT_READY_SUMMARY.md'
        ],
        
        # 设置和安装文档
        'docs/setup/': [
            'README.md'  # 保留原位置，但创建副本
        ],
        
        # MCP配置文件
        'configs/mcp/': [
            'claude_desktop_config.json',
            'complete-mcp-config.json',
            'mcp-config.json',
            'mcp_import_config.json',
            '.mcp.json'
        ],
        
        # 安装和配置脚本
        'scripts/setup/': [
            'configure-mcp-servers.bat',
            'start-claudia.bat',
            'start-claudia-mcp.bat',
            'start-claudia-with-mcp.bat',
            'restart-claudia.bat'
        ],
        
        # MCP导入脚本
        'scripts/mcp/': [
            'auto-configure-mcp.py',
            'auto-import-mcp.js',
            'batch-import-mcp.js',
            'browser-import-script.js',
            'direct-import-mcp.js'
        ],
        
        # 导入助手
        'tools/import/': [
            'import-helper.bat',
            'import-helper-en.bat'
        ],
        
        # 测试脚本
        'tools/testing/': [
            'test-all-mcp-servers.py',
            'test-mcp-servers.py',
            'verify-mcp-setup.py'
        ]
    }
    
    # 执行文件移动
    for target_dir, files in file_moves.items():
        for file_name in files:
            source_path = Path(file_name)
            target_path = Path(target_dir) / file_name
            
            if source_path.exists():
                try:
                    # 确保目标目录存在
                    target_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    # 移动文件
                    shutil.move(str(source_path), str(target_path))
                    print(f"📁 移动: {file_name} -> {target_dir}")
                except Exception as e:
                    print(f"❌ 移动失败 {file_name}: {e}")
            else:
                print(f"⚠️ 文件不存在: {file_name}")

def create_index_files():
    """创建各目录的索引文件"""
    
    # 创建主README
    main_readme = """# 📁 Claudia项目文件结构

## 📋 目录说明

### 📖 docs/ - 文档目录
- `mcp/` - MCP服务器相关文档
- `setup/` - 安装和设置文档
- `guides/` - 使用指南

### ⚙️ configs/ - 配置文件
- `mcp/` - MCP服务器配置文件

### 🔧 scripts/ - 脚本文件
- `setup/` - 安装和启动脚本
- `mcp/` - MCP相关脚本

### 🛠️ tools/ - 工具文件
- `import/` - 导入助手工具
- `testing/` - 测试脚本

### 💾 backup/ - 备份文件
- `old-configs/` - 旧配置文件备份

## 🚀 快速开始

1. **启动Claudia**: 运行 `scripts/setup/start-claudia.bat`
2. **配置MCP**: 参考 `docs/mcp/FINAL_MCP_CONFIGURATION.md`
3. **导入服务器**: 使用 `tools/import/import-helper-en.bat`
4. **测试功能**: 运行 `tools/testing/test-all-mcp-servers.py`

## 📚 重要文档

- [MCP配置指南](docs/mcp/FINAL_MCP_CONFIGURATION.md)
- [导入步骤](docs/mcp/STEP_BY_STEP_IMPORT.md)
- [项目总结](docs/mcp/IMPORT_READY_SUMMARY.md)
"""
    
    with open('PROJECT_STRUCTURE.md', 'w', encoding='utf-8') as f:
        f.write(main_readme)
    
    # 创建MCP文档索引
    mcp_index = """# 📦 MCP服务器文档索引

## 📋 配置文档
- `FINAL_MCP_CONFIGURATION.md` - 完整配置指南
- `MANUAL_MCP_SETUP.md` - 手动设置指南
- `STEP_BY_STEP_IMPORT.md` - 详细导入步骤

## 📊 状态报告
- `MCP_BATCH_IMPORT_SUCCESS.md` - 批量导入成功报告
- `MCP_DEPLOYMENT_COMPLETE.md` - 部署完成报告
- `MCP_IMPORT_SUCCESS_REPORT.md` - 导入成功报告
- `IMPORT_READY_SUMMARY.md` - 准备就绪总结

## 🎯 快速开始
1. 阅读 `FINAL_MCP_CONFIGURATION.md`
2. 按照 `STEP_BY_STEP_IMPORT.md` 导入
3. 查看 `IMPORT_READY_SUMMARY.md` 确认状态
"""
    
    with open('docs/mcp/README.md', 'w', encoding='utf-8') as f:
        f.write(mcp_index)
    
    # 创建配置文件索引
    config_index = """# ⚙️ MCP配置文件说明

## 📁 配置文件列表

### 主要配置文件
- `.mcp.json` - Claudia项目配置文件 (推荐使用)
- `complete-mcp-config.json` - 完整的9个服务器配置
- `mcp_import_config.json` - 导入专用配置文件

### 兼容性配置
- `claude_desktop_config.json` - Claude Desktop格式
- `mcp-config.json` - 通用MCP配置

## 🚀 使用方法
1. 在Claudia中导入 `.mcp.json`
2. 或使用任何其他配置文件进行导入
3. 所有文件包含相同的9个MCP服务器配置
"""
    
    with open('configs/mcp/README.md', 'w', encoding='utf-8') as f:
        f.write(config_index)

def create_quick_access_scripts():
    """创建快速访问脚本"""
    
    # 创建主启动脚本
    main_launcher = """@echo off
title Claudia项目启动器
echo.
echo ========================================
echo    Claudia项目启动器
echo ========================================
echo.
echo 选择操作:
echo 1. 启动Claudia
echo 2. 配置MCP服务器
echo 3. 测试MCP服务器
echo 4. 查看项目结构
echo 5. 退出
echo.
set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" (
    echo 启动Claudia...
    call scripts\\setup\\start-claudia.bat
) else if "%choice%"=="2" (
    echo 打开MCP配置助手...
    call tools\\import\\import-helper-en.bat
) else if "%choice%"=="3" (
    echo 运行MCP测试...
    python tools\\testing\\test-all-mcp-servers.py
    pause
) else if "%choice%"=="4" (
    echo 打开项目结构文档...
    start PROJECT_STRUCTURE.md
) else if "%choice%"=="5" (
    exit
) else (
    echo 无效选择，请重试
    pause
    goto :eof
)
"""
    
    with open('launch.bat', 'w', encoding='utf-8') as f:
        f.write(main_launcher)

def main():
    """主函数"""
    print("🚀 开始整理Claudia项目文件...")
    print("=" * 50)
    
    # 1. 创建目录结构
    print("\n📁 创建目录结构...")
    create_directory_structure()
    
    # 2. 整理文件
    print("\n📦 整理文件...")
    organize_files()
    
    # 3. 创建索引文件
    print("\n📝 创建索引文件...")
    create_index_files()
    
    # 4. 创建快速访问脚本
    print("\n🔧 创建快速访问脚本...")
    create_quick_access_scripts()
    
    print("\n" + "=" * 50)
    print("🎉 项目文件整理完成！")
    print("\n📋 新的项目结构:")
    print("├── docs/           # 文档")
    print("│   ├── mcp/        # MCP文档")
    print("│   └── setup/      # 设置文档")
    print("├── configs/        # 配置文件")
    print("│   └── mcp/        # MCP配置")
    print("├── scripts/        # 脚本文件")
    print("│   ├── setup/      # 启动脚本")
    print("│   └── mcp/        # MCP脚本")
    print("├── tools/          # 工具")
    print("│   ├── import/     # 导入工具")
    print("│   └── testing/    # 测试工具")
    print("└── backup/         # 备份")
    print()
    print("🚀 快速开始: 运行 launch.bat")
    print("📖 查看结构: PROJECT_STRUCTURE.md")

if __name__ == "__main__":
    main()
