{"rustc": 1842507548689473721, "features": "[\"alloc\", \"ring\", \"std\"]", "declared_features": "[\"alloc\", \"aws-lc-rs\", \"aws-lc-rs-fips\", \"default\", \"ring\", \"std\"]", "target": 5054897795206437336, "profile": 15657897354478470176, "path": 12590007092919164603, "deps": [[2883436298747778685, "pki_types", false, 7785388080773837983], [5491919304041016563, "ring", false, 10639158057842346926], [8995469080876806959, "untrusted", false, 7344650366984579428]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-webpki-6f967b2dc25ecf22\\dep-lib-webpki", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}