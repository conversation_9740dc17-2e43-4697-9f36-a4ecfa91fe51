{"rustc": 1842507548689473721, "features": "[\"charset\", \"cookies\", \"default\", \"http2\", \"macos-system-configuration\", \"rustls-tls\"]", "declared_features": "[\"blocking\", \"brotli\", \"charset\", \"cookies\", \"dangerous-settings\", \"default\", \"deflate\", \"gzip\", \"http2\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"socks\", \"stream\", \"tracing\", \"trust-dns\", \"unsafe-headers\", \"zstd\"]", "target": 7795770796983219439, "profile": 15657897354478470176, "path": 2927926812607995470, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 127465540148564513], [3150220818285335163, "url", false, 12833560258718765385], [8218178811151724123, "reqwest", false, 910772910244018617], [8298091525883606470, "cookie_store", false, 16569849201009642146], [9010263965687315507, "http", false, 15629939712500996649], [9451456094439810778, "regex", false, 2432167459552640842], [9538054652646069845, "tokio", false, 17016848415587674367], [9689903380558560274, "serde", false, 13578168418265350052], [10755362358622467486, "tauri", false, 9583120891350849165], [10806645703491011684, "thiserror", false, 983470577670415442], [13890802266741835355, "tauri_plugin_fs", false, 16101976588577041406], [15367738274754116744, "serde_json", false, 15734816997775649336], [15441187897486245138, "build_script_build", false, 4651140424611054868], [16066129441945555748, "bytes", false, 2626033548966229242], [17047088963840213854, "data_url", false, 6232834256312602976]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-http-2a9f92d761357932\\dep-lib-tauri_plugin_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}