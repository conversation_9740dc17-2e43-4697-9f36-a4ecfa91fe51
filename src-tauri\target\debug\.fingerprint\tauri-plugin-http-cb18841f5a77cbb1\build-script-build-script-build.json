{"rustc": 1842507548689473721, "features": "[\"charset\", \"cookies\", \"default\", \"http2\", \"macos-system-configuration\", \"rustls-tls\"]", "declared_features": "[\"blocking\", \"brotli\", \"charset\", \"cookies\", \"dangerous-settings\", \"default\", \"deflate\", \"gzip\", \"http2\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"socks\", \"stream\", \"tracing\", \"trust-dns\", \"unsafe-headers\", \"zstd\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 15611155942187385427, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 16100582569608480575], [2326493920556799156, "tauri_plugin", false, 1494466631916783358], [3150220818285335163, "url", false, 3744897104065425490], [6913375703034175521, "schemars", false, 4311322490016638138], [9451456094439810778, "regex", false, 2432167459552640842], [9689903380558560274, "serde", false, 8619023586440547223]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-http-cb18841f5a77cbb1\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}