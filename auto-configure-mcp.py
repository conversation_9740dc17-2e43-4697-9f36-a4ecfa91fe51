#!/usr/bin/env python3
"""
自动配置Claudia MCP服务器脚本
通过HTTP API自动添加所有MCP服务器到Claudia
"""

import requests
import json
import time
import sys
from pathlib import Path

# Claudia API配置
CLAUDIA_API_BASE = "http://localhost:1420"
TAURI_API_URL = f"{CLAUDIA_API_BASE}/__tauri_api__"

def call_tauri_api(command, payload=None):
    """调用Tauri API"""
    if payload is None:
        payload = {}
    
    data = {
        "cmd": command,
        "payload": payload
    }
    
    try:
        response = requests.post(TAURI_API_URL, json=data, timeout=30)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"❌ API调用失败: {e}")
        return None

def add_mcp_server(name, command, args, env=None, scope="project"):
    """添加MCP服务器"""
    if env is None:
        env = {}
    
    print(f"🔧 添加MCP服务器: {name}")
    
    payload = {
        "name": name,
        "transport": "stdio",
        "command": command,
        "args": args,
        "env": env,
        "url": None,
        "scope": scope
    }
    
    result = call_tauri_api("mcp_add", payload)
    
    if result and result.get("success"):
        print(f"✅ {name} 服务器添加成功")
        return True
    else:
        error_msg = result.get("message", "未知错误") if result else "API调用失败"
        print(f"❌ {name} 服务器添加失败: {error_msg}")
        return False

def list_mcp_servers():
    """列出所有MCP服务器"""
    print("📋 获取当前MCP服务器列表...")
    
    result = call_tauri_api("mcp_list")
    
    if result:
        print(f"当前已配置 {len(result)} 个MCP服务器:")
        for server in result:
            status = "✅ 运行中" if server.get("status", {}).get("running") else "⏸️ 停止"
            print(f"  - {server['name']} ({server['transport']}) {status}")
        return result
    else:
        print("❌ 无法获取服务器列表")
        return []

def remove_all_servers():
    """移除所有现有的MCP服务器"""
    servers = list_mcp_servers()
    
    if not servers:
        print("📝 没有需要移除的服务器")
        return
    
    print("🗑️ 移除现有的MCP服务器...")
    
    for server in servers:
        name = server['name']
        print(f"🗑️ 移除服务器: {name}")
        
        result = call_tauri_api("mcp_remove", {"name": name})
        
        if result:
            print(f"✅ {name} 移除成功")
        else:
            print(f"❌ {name} 移除失败")
        
        time.sleep(0.5)  # 避免API调用过快

def main():
    """主函数"""
    print("🚀 开始自动配置Claudia MCP服务器...")
    print("=" * 60)
    
    # 检查Claudia是否运行
    try:
        response = requests.get(CLAUDIA_API_BASE, timeout=5)
        print("✅ Claudia应用正在运行")
    except requests.exceptions.RequestException:
        print("❌ Claudia应用未运行，请先启动Claudia")
        print("   运行命令: npm run tauri dev")
        return 1
    
    # 读取MCP配置
    config_file = Path(".mcp.json")
    if not config_file.exists():
        print("❌ 找不到 .mcp.json 配置文件")
        return 1
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return 1
    
    mcp_servers = config.get("mcpServers", {})
    if not mcp_servers:
        print("❌ 配置文件中没有找到MCP服务器配置")
        return 1
    
    print(f"📋 找到 {len(mcp_servers)} 个MCP服务器配置")
    print()
    
    # 移除现有服务器（可选）
    user_input = input("是否要移除现有的MCP服务器? (y/N): ").strip().lower()
    if user_input in ['y', 'yes']:
        remove_all_servers()
        print()
    
    # 添加新服务器
    print("🔧 开始添加MCP服务器...")
    print()
    
    success_count = 0
    total_count = len(mcp_servers)
    
    for name, server_config in mcp_servers.items():
        command = server_config.get("command")
        args = server_config.get("args", [])
        env = server_config.get("env", {})
        
        if not command:
            print(f"❌ {name}: 缺少command配置")
            continue
        
        if add_mcp_server(name, command, args, env):
            success_count += 1
        
        time.sleep(1)  # 避免API调用过快
        print()
    
    # 显示结果
    print("=" * 60)
    print("📊 配置结果:")
    print(f"✅ 成功: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 所有MCP服务器配置完成！")
        print()
        print("下一步:")
        print("1. 在Claudia中查看MCP服务器状态")
        print("2. 测试MCP功能是否正常工作")
        print("3. 开始使用增强的AI助手功能")
        return 0
    else:
        print("⚠️ 部分服务器配置失败，请检查错误信息")
        return 1

if __name__ == "__main__":
    sys.exit(main())
