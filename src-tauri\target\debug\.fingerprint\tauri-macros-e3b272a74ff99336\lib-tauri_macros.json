{"rustc": 1842507548689473721, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 8778928345189965635, "deps": [[3060637413840920116, "proc_macro2", false, 5373005460213438867], [7341521034400937459, "tauri_codegen", false, 5283693812644199889], [11050281405049894993, "tauri_utils", false, 8557585670532656720], [13077543566650298139, "heck", false, 12227043807642275620], [17990358020177143287, "quote", false, 9227116837396871820], [18149961000318489080, "syn", false, 14812274515719501434]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-e3b272a74ff99336\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}