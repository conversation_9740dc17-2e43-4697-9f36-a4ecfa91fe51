<HTML>
<HEAD>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html" charset="utf-8">
<META NAME="Generator" CONTENT="Microsoft Word 97">
<TITLE>win32com</TITLE>
<META NAME="Template" CONTENT="C:\Program Files\Microsoft Office\Office\html.dot">
</HEAD>
<BODY TEXT="#000000" LINK="#0000ff" VLINK="#0000ff">
<DIR>

<P><!-- Enclose the entire page in UL, so bullets don't indent. --></P>
<H1><IMG SRC="image/pycom_blowing.gif" WIDTH=549 HEIGHT=99></H1>
<H2>Python and COM</H2>
<H3>Introduction</H3>
<P>Python has an excellent interface to COM (also known variously as OLE2, ActiveX, etc).</P>
<P>The Python COM package can be used to interface to almost any COM program (such as the MS-Office suite), write servers that can be hosted by any COM client (such as Visual Basic or C++), and has even been used to provide the core ActiveX Scripting Support. </P>


<UL>
<LI>Note that win32com is now released in the win32all installation package. The <A HREF="../win32all/win32all.exe">installation EXE can be downloaded</A>, or you <A HREF="../win32all/">can jump to the win32all readme</A> for more details. </LI>
<LI>Here is the <A HREF="win32com_src.zip">win32com source code</A> in a zip file. </LI></UL>
</DIR>
<DIR>

<H3>Documentation</H3>
<P><A HREF="ActiveXScripting.html">Preliminary Active Scripting and Debugging documentation</A> is available.</P>
<P>2 Quick-Start guides have been provided, which also contain other links. See the <A HREF="QuickStartClientCom.html">Quick Start for Client side COM</A> and the <A HREF="QuickStartServerCom.html">Quick Start for Server side COM</A> </P>
</P></DIR>
</DIR>
</BODY>
</HTML>
