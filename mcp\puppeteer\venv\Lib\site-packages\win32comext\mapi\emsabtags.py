# nopycln: file # Re-exporting many constants
# Converted "manually" from EMSABTAG.H
from .mapitags import (
    PROP_TAG,
    PT_APPTIME,
    PT_BINARY,
    PT_BOOLEAN,
    PT_CLSID,
    PT_CURRENCY,
    PT_DOUBLE,
    PT_ERROR,
    PT_FLOAT,
    PT_I2,
    PT_I4,
    PT_I8,
    PT_LONG,
    PT_LONGLONG,
    PT_MV_APPTIME,
    PT_MV_BINARY,
    PT_MV_CLSID,
    PT_MV_CURRENCY,
    PT_MV_DOUBLE,
    PT_MV_FLOAT,
    PT_MV_I2,
    PT_MV_I4,
    PT_MV_I8,
    PT_MV_LONG,
    PT_MV_LONGLONG,
    PT_MV_R4,
    PT_MV_R8,
    PT_MV_SHORT,
    PT_MV_STRING8,
    PT_MV_SYSTIME,
    PT_MV_TSTRING,
    PT_MV_UNICODE,
    PT_NULL,
    PT_OBJECT,
    PT_R4,
    PT_SHORT,
    PT_STRING8,
    PT_SYSTIME,
    PT_TSTRING,
    PT_UNICODE,
    PT_UNSPECIFIED,
)

AB_SHOW_PHANTOMS = 2
AB_SHOW_OTHERS = 4

# Flags for ulFlag on ResolveNames
EMS_AB_ADDRESS_LOOKUP = 1


# Constructed, but externally visible.
PR_EMS_AB_SERVER = PROP_TAG(PT_TSTRING, 65534)
PR_EMS_AB_SERVER_A = PROP_TAG(PT_STRING8, 65534)
PR_EMS_AB_SERVER_W = PROP_TAG(PT_UNICODE, 65534)
PR_EMS_AB_CONTAINERID = PROP_TAG(PT_LONG, 65533)
PR_EMS_AB_DOS_ENTRYID = PR_EMS_AB_CONTAINERID
PR_EMS_AB_PARENT_ENTRYID = PROP_TAG(PT_BINARY, 65532)
PR_EMS_AB_IS_MASTER = PROP_TAG(PT_BOOLEAN, 65531)
PR_EMS_AB_OBJECT_OID = PROP_TAG(PT_BINARY, 65530)
PR_EMS_AB_HIERARCHY_PATH = PROP_TAG(PT_TSTRING, 65529)
PR_EMS_AB_HIERARCHY_PATH_A = PROP_TAG(PT_STRING8, 65529)
PR_EMS_AB_HIERARCHY_PATH_W = PROP_TAG(PT_UNICODE, 65529)
PR_EMS_AB_CHILD_RDNS = PROP_TAG(PT_MV_STRING8, 65528)

MIN_EMS_AB_CONSTRUCTED_PROP_ID = 65528

PR_EMS_AB_OTHER_RECIPS = PROP_TAG(PT_OBJECT, 61440)

# Prop tags defined in the schema.
PR_EMS_AB_DISPLAY_NAME_PRINTABLE = PROP_TAG(PT_TSTRING, 14847)
PR_EMS_AB_DISPLAY_NAME_PRINTABLE_A = PROP_TAG(PT_STRING8, 14847)
PR_EMS_AB_DISPLAY_NAME_PRINTABLE_W = PROP_TAG(PT_UNICODE, 14847)

PR_EMS_AB_ACCESS_CATEGORY = PROP_TAG(PT_LONG, 32836)
PR_EMS_AB_ACTIVATION_SCHEDULE = PROP_TAG(PT_BINARY, 32837)
PR_EMS_AB_ACTIVATION_STYLE = PROP_TAG(PT_LONG, 32838)
PR_EMS_AB_ADDRESS_ENTRY_DISPLAY_TABLE = PROP_TAG(PT_BINARY, 32791)
PR_EMS_AB_ADDRESS_ENTRY_DISPLAY_TABLE_MSDOS = PROP_TAG(PT_BINARY, 32839)
PR_EMS_AB_ADDRESS_SYNTAX = PROP_TAG(PT_BINARY, 32792)
PR_EMS_AB_ADDRESS_TYPE = PROP_TAG(PT_TSTRING, 32840)
PR_EMS_AB_ADDRESS_TYPE_A = PROP_TAG(PT_STRING8, 32840)
PR_EMS_AB_ADDRESS_TYPE_W = PROP_TAG(PT_UNICODE, 32840)
PR_EMS_AB_ADMD = PROP_TAG(PT_TSTRING, 32841)
PR_EMS_AB_ADMD_A = PROP_TAG(PT_STRING8, 32841)
PR_EMS_AB_ADMD_W = PROP_TAG(PT_UNICODE, 32841)
PR_EMS_AB_ADMIN_DESCRIPTION = PROP_TAG(PT_TSTRING, 32842)
PR_EMS_AB_ADMIN_DESCRIPTION_A = PROP_TAG(PT_STRING8, 32842)
PR_EMS_AB_ADMIN_DESCRIPTION_W = PROP_TAG(PT_UNICODE, 32842)
PR_EMS_AB_ADMIN_DISPLAY_NAME = PROP_TAG(PT_TSTRING, 32843)
PR_EMS_AB_ADMIN_DISPLAY_NAME_A = PROP_TAG(PT_STRING8, 32843)
PR_EMS_AB_ADMIN_DISPLAY_NAME_W = PROP_TAG(PT_UNICODE, 32843)
PR_EMS_AB_ADMIN_EXTENSION_DLL = PROP_TAG(PT_TSTRING, 32844)
PR_EMS_AB_ADMIN_EXTENSION_DLL_A = PROP_TAG(PT_STRING8, 32844)
PR_EMS_AB_ADMIN_EXTENSION_DLL_W = PROP_TAG(PT_UNICODE, 32844)
PR_EMS_AB_ALIASED_OBJECT_NAME = PROP_TAG(PT_TSTRING, 32845)
PR_EMS_AB_ALIASED_OBJECT_NAME_A = PROP_TAG(PT_STRING8, 32845)
PR_EMS_AB_ALIASED_OBJECT_NAME_W = PROP_TAG(PT_UNICODE, 32845)
PR_EMS_AB_ALIASED_OBJECT_NAME_O = PROP_TAG(PT_OBJECT, 32845)
PR_EMS_AB_ALIASED_OBJECT_NAME_T = PROP_TAG(PT_TSTRING, 32845)
PR_EMS_AB_ALT_RECIPIENT = PROP_TAG(PT_TSTRING, 32846)
PR_EMS_AB_ALT_RECIPIENT_A = PROP_TAG(PT_STRING8, 32846)
PR_EMS_AB_ALT_RECIPIENT_W = PROP_TAG(PT_UNICODE, 32846)
PR_EMS_AB_ALT_RECIPIENT_O = PROP_TAG(PT_OBJECT, 32846)
PR_EMS_AB_ALT_RECIPIENT_T = PROP_TAG(PT_TSTRING, 32846)
PR_EMS_AB_ALT_RECIPIENT_BL = PROP_TAG(PT_MV_TSTRING, 32847)
PR_EMS_AB_ALT_RECIPIENT_BL_A = PROP_TAG(PT_MV_STRING8, 32847)
PR_EMS_AB_ALT_RECIPIENT_BL_W = PROP_TAG(PT_MV_UNICODE, 32847)
PR_EMS_AB_ALT_RECIPIENT_BL_O = PROP_TAG(PT_OBJECT, 32847)
PR_EMS_AB_ALT_RECIPIENT_BL_T = PROP_TAG(PT_MV_TSTRING, 32847)
PR_EMS_AB_ANCESTOR_ID = PROP_TAG(PT_BINARY, 32848)
PR_EMS_AB_ASSOC_NT_ACCOUNT = PROP_TAG(PT_BINARY, 32807)
PR_EMS_AB_ASSOC_REMOTE_DXA = PROP_TAG(PT_MV_TSTRING, 32849)
PR_EMS_AB_ASSOC_REMOTE_DXA_A = PROP_TAG(PT_MV_STRING8, 32849)
PR_EMS_AB_ASSOC_REMOTE_DXA_W = PROP_TAG(PT_MV_UNICODE, 32849)
PR_EMS_AB_ASSOC_REMOTE_DXA_O = PROP_TAG(PT_OBJECT, 32849)
PR_EMS_AB_ASSOC_REMOTE_DXA_T = PROP_TAG(PT_MV_TSTRING, 32849)
PR_EMS_AB_ASSOCIATION_LIFETIME = PROP_TAG(PT_LONG, 32850)
PR_EMS_AB_AUTH_ORIG_BL = PROP_TAG(PT_MV_TSTRING, 32851)
PR_EMS_AB_AUTH_ORIG_BL_A = PROP_TAG(PT_MV_STRING8, 32851)
PR_EMS_AB_AUTH_ORIG_BL_W = PROP_TAG(PT_MV_UNICODE, 32851)
PR_EMS_AB_AUTH_ORIG_BL_O = PROP_TAG(PT_OBJECT, 32851)
PR_EMS_AB_AUTH_ORIG_BL_T = PROP_TAG(PT_MV_TSTRING, 32851)
PR_EMS_AB_AUTHORITY_REVOCATION_LIST = PROP_TAG(PT_MV_BINARY, 32806)
PR_EMS_AB_AUTHORIZED_DOMAIN = PROP_TAG(PT_TSTRING, 32852)
PR_EMS_AB_AUTHORIZED_DOMAIN_A = PROP_TAG(PT_STRING8, 32852)
PR_EMS_AB_AUTHORIZED_DOMAIN_W = PROP_TAG(PT_UNICODE, 32852)
PR_EMS_AB_AUTHORIZED_PASSWORD = PROP_TAG(PT_BINARY, 32853)
PR_EMS_AB_AUTHORIZED_USER = PROP_TAG(PT_TSTRING, 32854)
PR_EMS_AB_AUTHORIZED_USER_A = PROP_TAG(PT_STRING8, 32854)
PR_EMS_AB_AUTHORIZED_USER_W = PROP_TAG(PT_UNICODE, 32854)
PR_EMS_AB_AUTOREPLY = PROP_TAG(PT_BOOLEAN, 32779)
PR_EMS_AB_AUTOREPLY_MESSAGE = PROP_TAG(PT_TSTRING, 32778)
PR_EMS_AB_AUTOREPLY_MESSAGE_A = PROP_TAG(PT_STRING8, 32778)
PR_EMS_AB_AUTOREPLY_MESSAGE_W = PROP_TAG(PT_UNICODE, 32778)
PR_EMS_AB_AUTOREPLY_SUBJECT = PROP_TAG(PT_TSTRING, 32830)
PR_EMS_AB_AUTOREPLY_SUBJECT_A = PROP_TAG(PT_STRING8, 32830)
PR_EMS_AB_AUTOREPLY_SUBJECT_W = PROP_TAG(PT_UNICODE, 32830)
PR_EMS_AB_BRIDGEHEAD_SERVERS = PROP_TAG(PT_MV_TSTRING, 33140)
PR_EMS_AB_BRIDGEHEAD_SERVERS_A = PROP_TAG(PT_MV_STRING8, 33140)
PR_EMS_AB_BRIDGEHEAD_SERVERS_W = PROP_TAG(PT_MV_UNICODE, 33140)
PR_EMS_AB_BRIDGEHEAD_SERVERS_O = PROP_TAG(PT_OBJECT, 33140)
PR_EMS_AB_BRIDGEHEAD_SERVERS_T = PROP_TAG(PT_MV_TSTRING, 33140)
PR_EMS_AB_BUSINESS_CATEGORY = PROP_TAG(PT_MV_TSTRING, 32855)
PR_EMS_AB_BUSINESS_CATEGORY_A = PROP_TAG(PT_MV_STRING8, 32855)
PR_EMS_AB_BUSINESS_CATEGORY_W = PROP_TAG(PT_MV_UNICODE, 32855)
PR_EMS_AB_BUSINESS_ROLES = PROP_TAG(PT_BINARY, 32803)
PR_EMS_AB_CA_CERTIFICATE = PROP_TAG(PT_MV_BINARY, 32771)
PR_EMS_AB_CAN_CREATE_PF = PROP_TAG(PT_MV_TSTRING, 32856)
PR_EMS_AB_CAN_CREATE_PF_A = PROP_TAG(PT_MV_STRING8, 32856)
PR_EMS_AB_CAN_CREATE_PF_W = PROP_TAG(PT_MV_UNICODE, 32856)
PR_EMS_AB_CAN_CREATE_PF_O = PROP_TAG(PT_OBJECT, 32856)
PR_EMS_AB_CAN_CREATE_PF_T = PROP_TAG(PT_MV_TSTRING, 32856)
PR_EMS_AB_CAN_CREATE_PF_BL = PROP_TAG(PT_MV_TSTRING, 32857)
PR_EMS_AB_CAN_CREATE_PF_BL_A = PROP_TAG(PT_MV_STRING8, 32857)
PR_EMS_AB_CAN_CREATE_PF_BL_W = PROP_TAG(PT_MV_UNICODE, 32857)
PR_EMS_AB_CAN_CREATE_PF_BL_O = PROP_TAG(PT_OBJECT, 32857)
PR_EMS_AB_CAN_CREATE_PF_BL_T = PROP_TAG(PT_MV_TSTRING, 32857)
PR_EMS_AB_CAN_CREATE_PF_DL = PROP_TAG(PT_MV_TSTRING, 32858)
PR_EMS_AB_CAN_CREATE_PF_DL_A = PROP_TAG(PT_MV_STRING8, 32858)
PR_EMS_AB_CAN_CREATE_PF_DL_W = PROP_TAG(PT_MV_UNICODE, 32858)
PR_EMS_AB_CAN_CREATE_PF_DL_O = PROP_TAG(PT_OBJECT, 32858)
PR_EMS_AB_CAN_CREATE_PF_DL_T = PROP_TAG(PT_MV_TSTRING, 32858)
PR_EMS_AB_CAN_CREATE_PF_DL_BL = PROP_TAG(PT_MV_TSTRING, 32859)
PR_EMS_AB_CAN_CREATE_PF_DL_BL_A = PROP_TAG(PT_MV_STRING8, 32859)
PR_EMS_AB_CAN_CREATE_PF_DL_BL_W = PROP_TAG(PT_MV_UNICODE, 32859)
PR_EMS_AB_CAN_CREATE_PF_DL_BL_O = PROP_TAG(PT_OBJECT, 32859)
PR_EMS_AB_CAN_CREATE_PF_DL_BL_T = PROP_TAG(PT_MV_TSTRING, 32859)
PR_EMS_AB_CAN_NOT_CREATE_PF = PROP_TAG(PT_MV_TSTRING, 32860)
PR_EMS_AB_CAN_NOT_CREATE_PF_A = PROP_TAG(PT_MV_STRING8, 32860)
PR_EMS_AB_CAN_NOT_CREATE_PF_W = PROP_TAG(PT_MV_UNICODE, 32860)
PR_EMS_AB_CAN_NOT_CREATE_PF_O = PROP_TAG(PT_OBJECT, 32860)
PR_EMS_AB_CAN_NOT_CREATE_PF_T = PROP_TAG(PT_MV_TSTRING, 32860)
PR_EMS_AB_CAN_NOT_CREATE_PF_BL = PROP_TAG(PT_MV_TSTRING, 32861)
PR_EMS_AB_CAN_NOT_CREATE_PF_BL_A = PROP_TAG(PT_MV_STRING8, 32861)
PR_EMS_AB_CAN_NOT_CREATE_PF_BL_W = PROP_TAG(PT_MV_UNICODE, 32861)
PR_EMS_AB_CAN_NOT_CREATE_PF_BL_O = PROP_TAG(PT_OBJECT, 32861)
PR_EMS_AB_CAN_NOT_CREATE_PF_BL_T = PROP_TAG(PT_MV_TSTRING, 32861)
PR_EMS_AB_CAN_NOT_CREATE_PF_DL = PROP_TAG(PT_MV_TSTRING, 32862)
PR_EMS_AB_CAN_NOT_CREATE_PF_DL_A = PROP_TAG(PT_MV_STRING8, 32862)
PR_EMS_AB_CAN_NOT_CREATE_PF_DL_W = PROP_TAG(PT_MV_UNICODE, 32862)
PR_EMS_AB_CAN_NOT_CREATE_PF_DL_O = PROP_TAG(PT_OBJECT, 32862)
PR_EMS_AB_CAN_NOT_CREATE_PF_DL_T = PROP_TAG(PT_MV_TSTRING, 32862)
PR_EMS_AB_CAN_NOT_CREATE_PF_DL_BL = PROP_TAG(PT_MV_TSTRING, 32863)
PR_EMS_AB_CAN_NOT_CREATE_PF_DL_BL_A = PROP_TAG(PT_MV_STRING8, 32863)
PR_EMS_AB_CAN_NOT_CREATE_PF_DL_BL_W = PROP_TAG(PT_MV_UNICODE, 32863)
PR_EMS_AB_CAN_NOT_CREATE_PF_DL_BL_O = PROP_TAG(PT_OBJECT, 32863)
PR_EMS_AB_CAN_NOT_CREATE_PF_DL_BL_T = PROP_TAG(PT_MV_TSTRING, 32863)
PR_EMS_AB_CAN_PRESERVE_DNS = PROP_TAG(PT_BOOLEAN, 32864)
PR_EMS_AB_CERTIFICATE_REVOCATION_LIST = PROP_TAG(PT_BINARY, 32790)
PR_EMS_AB_CLOCK_ALERT_OFFSET = PROP_TAG(PT_LONG, 32865)
PR_EMS_AB_CLOCK_ALERT_REPAIR = PROP_TAG(PT_BOOLEAN, 32866)
PR_EMS_AB_CLOCK_WARNING_OFFSET = PROP_TAG(PT_LONG, 32867)
PR_EMS_AB_CLOCK_WARNING_REPAIR = PROP_TAG(PT_BOOLEAN, 32868)
PR_EMS_AB_COMPUTER_NAME = PROP_TAG(PT_TSTRING, 32869)
PR_EMS_AB_COMPUTER_NAME_A = PROP_TAG(PT_STRING8, 32869)
PR_EMS_AB_COMPUTER_NAME_W = PROP_TAG(PT_UNICODE, 32869)
PR_EMS_AB_CONNECTED_DOMAINS = PROP_TAG(PT_MV_TSTRING, 32870)
PR_EMS_AB_CONNECTED_DOMAINS_A = PROP_TAG(PT_MV_STRING8, 32870)
PR_EMS_AB_CONNECTED_DOMAINS_W = PROP_TAG(PT_MV_UNICODE, 32870)
PR_EMS_AB_CONTAINER_INFO = PROP_TAG(PT_LONG, 32871)
PR_EMS_AB_COST = PROP_TAG(PT_LONG, 32872)
PR_EMS_AB_COUNTRY_NAME = PROP_TAG(PT_TSTRING, 32873)
PR_EMS_AB_COUNTRY_NAME_A = PROP_TAG(PT_STRING8, 32873)
PR_EMS_AB_COUNTRY_NAME_W = PROP_TAG(PT_UNICODE, 32873)
PR_EMS_AB_CROSS_CERTIFICATE_PAIR = PROP_TAG(PT_MV_BINARY, 32805)
PR_EMS_AB_DELIV_CONT_LENGTH = PROP_TAG(PT_LONG, 32874)
PR_EMS_AB_DELIV_EITS = PROP_TAG(PT_MV_BINARY, 32875)
PR_EMS_AB_DELIV_EXT_CONT_TYPES = PROP_TAG(PT_MV_BINARY, 32876)
PR_EMS_AB_DELIVER_AND_REDIRECT = PROP_TAG(PT_BOOLEAN, 32877)
PR_EMS_AB_DELIVERY_MECHANISM = PROP_TAG(PT_LONG, 32878)
PR_EMS_AB_DESCRIPTION = PROP_TAG(PT_MV_TSTRING, 32879)
PR_EMS_AB_DESCRIPTION_A = PROP_TAG(PT_MV_STRING8, 32879)
PR_EMS_AB_DESCRIPTION_W = PROP_TAG(PT_MV_UNICODE, 32879)
PR_EMS_AB_DESTINATION_INDICATOR = PROP_TAG(PT_MV_TSTRING, 32880)
PR_EMS_AB_DESTINATION_INDICATOR_A = PROP_TAG(PT_MV_STRING8, 32880)
PR_EMS_AB_DESTINATION_INDICATOR_W = PROP_TAG(PT_MV_UNICODE, 32880)
PR_EMS_AB_DIAGNOSTIC_REG_KEY = PROP_TAG(PT_TSTRING, 32881)
PR_EMS_AB_DIAGNOSTIC_REG_KEY_A = PROP_TAG(PT_STRING8, 32881)
PR_EMS_AB_DIAGNOSTIC_REG_KEY_W = PROP_TAG(PT_UNICODE, 32881)
PR_EMS_AB_DISPLAY_NAME_OVERRIDE = PROP_TAG(PT_BOOLEAN, 32769)
PR_EMS_AB_DL_MEM_REJECT_PERMS_BL = PROP_TAG(PT_MV_TSTRING, 32882)
PR_EMS_AB_DL_MEM_REJECT_PERMS_BL_A = PROP_TAG(PT_MV_STRING8, 32882)
PR_EMS_AB_DL_MEM_REJECT_PERMS_BL_W = PROP_TAG(PT_MV_UNICODE, 32882)
PR_EMS_AB_DL_MEM_REJECT_PERMS_BL_O = PROP_TAG(PT_OBJECT, 32882)
PR_EMS_AB_DL_MEM_REJECT_PERMS_BL_T = PROP_TAG(PT_MV_TSTRING, 32882)
PR_EMS_AB_DL_MEM_SUBMIT_PERMS_BL = PROP_TAG(PT_MV_TSTRING, 32883)
PR_EMS_AB_DL_MEM_SUBMIT_PERMS_BL_A = PROP_TAG(PT_MV_STRING8, 32883)
PR_EMS_AB_DL_MEM_SUBMIT_PERMS_BL_W = PROP_TAG(PT_MV_UNICODE, 32883)
PR_EMS_AB_DL_MEM_SUBMIT_PERMS_BL_O = PROP_TAG(PT_OBJECT, 32883)
PR_EMS_AB_DL_MEM_SUBMIT_PERMS_BL_T = PROP_TAG(PT_MV_TSTRING, 32883)
PR_EMS_AB_DL_MEMBER_RULE = PROP_TAG(PT_MV_BINARY, 32884)
PR_EMS_AB_DOMAIN_DEF_ALT_RECIP = PROP_TAG(PT_TSTRING, 32885)
PR_EMS_AB_DOMAIN_DEF_ALT_RECIP_A = PROP_TAG(PT_STRING8, 32885)
PR_EMS_AB_DOMAIN_DEF_ALT_RECIP_W = PROP_TAG(PT_UNICODE, 32885)
PR_EMS_AB_DOMAIN_DEF_ALT_RECIP_O = PROP_TAG(PT_OBJECT, 32885)
PR_EMS_AB_DOMAIN_DEF_ALT_RECIP_T = PROP_TAG(PT_TSTRING, 32885)
PR_EMS_AB_DOMAIN_NAME = PROP_TAG(PT_TSTRING, 32886)
PR_EMS_AB_DOMAIN_NAME_A = PROP_TAG(PT_STRING8, 32886)
PR_EMS_AB_DOMAIN_NAME_W = PROP_TAG(PT_UNICODE, 32886)
PR_EMS_AB_DSA_SIGNATURE = PROP_TAG(PT_BINARY, 32887)
PR_EMS_AB_DXA_ADMIN_COPY = PROP_TAG(PT_BOOLEAN, 32888)
PR_EMS_AB_DXA_ADMIN_FORWARD = PROP_TAG(PT_BOOLEAN, 32889)
PR_EMS_AB_DXA_ADMIN_UPDATE = PROP_TAG(PT_LONG, 32890)
PR_EMS_AB_DXA_APPEND_REQCN = PROP_TAG(PT_BOOLEAN, 32891)
PR_EMS_AB_DXA_CONF_CONTAINER_LIST = PROP_TAG(PT_MV_TSTRING, 32892)
PR_EMS_AB_DXA_CONF_CONTAINER_LIST_A = PROP_TAG(PT_MV_STRING8, 32892)
PR_EMS_AB_DXA_CONF_CONTAINER_LIST_W = PROP_TAG(PT_MV_UNICODE, 32892)
PR_EMS_AB_DXA_CONF_CONTAINER_LIST_O = PROP_TAG(PT_OBJECT, 32892)
PR_EMS_AB_DXA_CONF_CONTAINER_LIST_T = PROP_TAG(PT_MV_TSTRING, 32892)
PR_EMS_AB_DXA_CONF_REQ_TIME = PROP_TAG(PT_SYSTIME, 32893)
PR_EMS_AB_DXA_CONF_SEQ = PROP_TAG(PT_TSTRING, 32894)
PR_EMS_AB_DXA_CONF_SEQ_A = PROP_TAG(PT_STRING8, 32894)
PR_EMS_AB_DXA_CONF_SEQ_W = PROP_TAG(PT_UNICODE, 32894)
PR_EMS_AB_DXA_CONF_SEQ_USN = PROP_TAG(PT_LONG, 32895)
PR_EMS_AB_DXA_EXCHANGE_OPTIONS = PROP_TAG(PT_LONG, 32896)
PR_EMS_AB_DXA_EXPORT_NOW = PROP_TAG(PT_BOOLEAN, 32897)
PR_EMS_AB_DXA_FLAGS = PROP_TAG(PT_LONG, 32898)
PR_EMS_AB_DXA_IMP_SEQ = PROP_TAG(PT_TSTRING, 32899)
PR_EMS_AB_DXA_IMP_SEQ_A = PROP_TAG(PT_STRING8, 32899)
PR_EMS_AB_DXA_IMP_SEQ_W = PROP_TAG(PT_UNICODE, 32899)
PR_EMS_AB_DXA_IMP_SEQ_TIME = PROP_TAG(PT_SYSTIME, 32900)
PR_EMS_AB_DXA_IMP_SEQ_USN = PROP_TAG(PT_LONG, 32901)
PR_EMS_AB_DXA_IMPORT_NOW = PROP_TAG(PT_BOOLEAN, 32902)
PR_EMS_AB_DXA_IN_TEMPLATE_MAP = PROP_TAG(PT_MV_TSTRING, 32903)
PR_EMS_AB_DXA_IN_TEMPLATE_MAP_A = PROP_TAG(PT_MV_STRING8, 32903)
PR_EMS_AB_DXA_IN_TEMPLATE_MAP_W = PROP_TAG(PT_MV_UNICODE, 32903)
PR_EMS_AB_DXA_LOCAL_ADMIN = PROP_TAG(PT_TSTRING, 32904)
PR_EMS_AB_DXA_LOCAL_ADMIN_A = PROP_TAG(PT_STRING8, 32904)
PR_EMS_AB_DXA_LOCAL_ADMIN_W = PROP_TAG(PT_UNICODE, 32904)
PR_EMS_AB_DXA_LOCAL_ADMIN_O = PROP_TAG(PT_OBJECT, 32904)
PR_EMS_AB_DXA_LOCAL_ADMIN_T = PROP_TAG(PT_TSTRING, 32904)
PR_EMS_AB_DXA_LOGGING_LEVEL = PROP_TAG(PT_LONG, 32905)
PR_EMS_AB_DXA_NATIVE_ADDRESS_TYPE = PROP_TAG(PT_TSTRING, 32906)
PR_EMS_AB_DXA_NATIVE_ADDRESS_TYPE_A = PROP_TAG(PT_STRING8, 32906)
PR_EMS_AB_DXA_NATIVE_ADDRESS_TYPE_W = PROP_TAG(PT_UNICODE, 32906)
PR_EMS_AB_DXA_OUT_TEMPLATE_MAP = PROP_TAG(PT_MV_TSTRING, 32907)
PR_EMS_AB_DXA_OUT_TEMPLATE_MAP_A = PROP_TAG(PT_MV_STRING8, 32907)
PR_EMS_AB_DXA_OUT_TEMPLATE_MAP_W = PROP_TAG(PT_MV_UNICODE, 32907)
PR_EMS_AB_DXA_PASSWORD = PROP_TAG(PT_TSTRING, 32908)
PR_EMS_AB_DXA_PASSWORD_A = PROP_TAG(PT_STRING8, 32908)
PR_EMS_AB_DXA_PASSWORD_W = PROP_TAG(PT_UNICODE, 32908)
PR_EMS_AB_DXA_PREV_EXCHANGE_OPTIONS = PROP_TAG(PT_LONG, 32909)
PR_EMS_AB_DXA_PREV_EXPORT_NATIVE_ONLY = PROP_TAG(PT_BOOLEAN, 32910)
PR_EMS_AB_DXA_PREV_IN_EXCHANGE_SENSITIVITY = PROP_TAG(PT_LONG, 32911)
PR_EMS_AB_DXA_PREV_REMOTE_ENTRIES = PROP_TAG(PT_TSTRING, 32912)
PR_EMS_AB_DXA_PREV_REMOTE_ENTRIES_A = PROP_TAG(PT_STRING8, 32912)
PR_EMS_AB_DXA_PREV_REMOTE_ENTRIES_W = PROP_TAG(PT_UNICODE, 32912)
PR_EMS_AB_DXA_PREV_REMOTE_ENTRIES_O = PROP_TAG(PT_OBJECT, 32912)
PR_EMS_AB_DXA_PREV_REMOTE_ENTRIES_T = PROP_TAG(PT_TSTRING, 32912)
PR_EMS_AB_DXA_PREV_REPLICATION_SENSITIVITY = PROP_TAG(PT_LONG, 32913)
PR_EMS_AB_DXA_PREV_TEMPLATE_OPTIONS = PROP_TAG(PT_LONG, 32914)
PR_EMS_AB_DXA_PREV_TYPES = PROP_TAG(PT_LONG, 32915)
PR_EMS_AB_DXA_RECIPIENT_CP = PROP_TAG(PT_TSTRING, 32916)
PR_EMS_AB_DXA_RECIPIENT_CP_A = PROP_TAG(PT_STRING8, 32916)
PR_EMS_AB_DXA_RECIPIENT_CP_W = PROP_TAG(PT_UNICODE, 32916)
PR_EMS_AB_DXA_REMOTE_CLIENT = PROP_TAG(PT_TSTRING, 32917)
PR_EMS_AB_DXA_REMOTE_CLIENT_A = PROP_TAG(PT_STRING8, 32917)
PR_EMS_AB_DXA_REMOTE_CLIENT_W = PROP_TAG(PT_UNICODE, 32917)
PR_EMS_AB_DXA_REMOTE_CLIENT_O = PROP_TAG(PT_OBJECT, 32917)
PR_EMS_AB_DXA_REMOTE_CLIENT_T = PROP_TAG(PT_TSTRING, 32917)
PR_EMS_AB_DXA_REQ_SEQ = PROP_TAG(PT_TSTRING, 32918)
PR_EMS_AB_DXA_REQ_SEQ_A = PROP_TAG(PT_STRING8, 32918)
PR_EMS_AB_DXA_REQ_SEQ_W = PROP_TAG(PT_UNICODE, 32918)
PR_EMS_AB_DXA_REQ_SEQ_TIME = PROP_TAG(PT_SYSTIME, 32919)
PR_EMS_AB_DXA_REQ_SEQ_USN = PROP_TAG(PT_LONG, 32920)
PR_EMS_AB_DXA_REQNAME = PROP_TAG(PT_TSTRING, 32921)
PR_EMS_AB_DXA_REQNAME_A = PROP_TAG(PT_STRING8, 32921)
PR_EMS_AB_DXA_REQNAME_W = PROP_TAG(PT_UNICODE, 32921)
PR_EMS_AB_DXA_SVR_SEQ = PROP_TAG(PT_TSTRING, 32922)
PR_EMS_AB_DXA_SVR_SEQ_A = PROP_TAG(PT_STRING8, 32922)
PR_EMS_AB_DXA_SVR_SEQ_W = PROP_TAG(PT_UNICODE, 32922)
PR_EMS_AB_DXA_SVR_SEQ_TIME = PROP_TAG(PT_SYSTIME, 32923)
PR_EMS_AB_DXA_SVR_SEQ_USN = PROP_TAG(PT_LONG, 32924)
PR_EMS_AB_DXA_TASK = PROP_TAG(PT_LONG, 32925)
PR_EMS_AB_DXA_TEMPLATE_OPTIONS = PROP_TAG(PT_LONG, 32926)
PR_EMS_AB_DXA_TEMPLATE_TIMESTAMP = PROP_TAG(PT_SYSTIME, 32927)
PR_EMS_AB_DXA_TYPES = PROP_TAG(PT_LONG, 32928)
PR_EMS_AB_DXA_UNCONF_CONTAINER_LIST = PROP_TAG(PT_MV_TSTRING, 32929)
PR_EMS_AB_DXA_UNCONF_CONTAINER_LIST_A = PROP_TAG(PT_MV_STRING8, 32929)
PR_EMS_AB_DXA_UNCONF_CONTAINER_LIST_W = PROP_TAG(PT_MV_UNICODE, 32929)
PR_EMS_AB_DXA_UNCONF_CONTAINER_LIST_O = PROP_TAG(PT_OBJECT, 32929)
PR_EMS_AB_DXA_UNCONF_CONTAINER_LIST_T = PROP_TAG(PT_MV_TSTRING, 32929)
PR_EMS_AB_ENABLED_PROTOCOLS = PROP_TAG(PT_LONG, 33151)
PR_EMS_AB_ENCAPSULATION_METHOD = PROP_TAG(PT_LONG, 32930)
PR_EMS_AB_ENCRYPT = PROP_TAG(PT_BOOLEAN, 32931)
PR_EMS_AB_ENCRYPT_ALG_LIST_NA = PROP_TAG(PT_MV_TSTRING, 32832)
PR_EMS_AB_ENCRYPT_ALG_LIST_NA_A = PROP_TAG(PT_MV_STRING8, 32832)
PR_EMS_AB_ENCRYPT_ALG_LIST_NA_W = PROP_TAG(PT_MV_UNICODE, 32832)
PR_EMS_AB_ENCRYPT_ALG_LIST_OTHER = PROP_TAG(PT_MV_TSTRING, 32833)
PR_EMS_AB_ENCRYPT_ALG_LIST_OTHER_A = PROP_TAG(PT_MV_STRING8, 32833)
PR_EMS_AB_ENCRYPT_ALG_LIST_OTHER_W = PROP_TAG(PT_MV_UNICODE, 32833)
PR_EMS_AB_ENCRYPT_ALG_SELECTED_NA = PROP_TAG(PT_TSTRING, 32835)
PR_EMS_AB_ENCRYPT_ALG_SELECTED_NA_A = PROP_TAG(PT_STRING8, 32835)
PR_EMS_AB_ENCRYPT_ALG_SELECTED_NA_W = PROP_TAG(PT_UNICODE, 32835)
PR_EMS_AB_ENCRYPT_ALG_SELECTED_OTHER = PROP_TAG(PT_TSTRING, 32829)
PR_EMS_AB_ENCRYPT_ALG_SELECTED_OTHER_A = PROP_TAG(PT_STRING8, 32829)
PR_EMS_AB_ENCRYPT_ALG_SELECTED_OTHER_W = PROP_TAG(PT_UNICODE, 32829)
PR_EMS_AB_EXPAND_DLS_LOCALLY = PROP_TAG(PT_BOOLEAN, 32932)
PR_EMS_AB_EXPIRATION_TIME = PROP_TAG(PT_SYSTIME, 32808)
PR_EMS_AB_EXPORT_CONTAINERS = PROP_TAG(PT_MV_TSTRING, 32933)
PR_EMS_AB_EXPORT_CONTAINERS_A = PROP_TAG(PT_MV_STRING8, 32933)
PR_EMS_AB_EXPORT_CONTAINERS_W = PROP_TAG(PT_MV_UNICODE, 32933)
PR_EMS_AB_EXPORT_CONTAINERS_O = PROP_TAG(PT_OBJECT, 32933)
PR_EMS_AB_EXPORT_CONTAINERS_T = PROP_TAG(PT_MV_TSTRING, 32933)
PR_EMS_AB_EXPORT_CUSTOM_RECIPIENTS = PROP_TAG(PT_BOOLEAN, 32934)
PR_EMS_AB_EXTENDED_CHARS_ALLOWED = PROP_TAG(PT_BOOLEAN, 32935)
PR_EMS_AB_EXTENSION_ATTRIBUTE_1 = PROP_TAG(PT_TSTRING, 32813)
PR_EMS_AB_EXTENSION_ATTRIBUTE_1_A = PROP_TAG(PT_STRING8, 32813)
PR_EMS_AB_EXTENSION_ATTRIBUTE_1_W = PROP_TAG(PT_UNICODE, 32813)
PR_EMS_AB_EXTENSION_ATTRIBUTE_10 = PROP_TAG(PT_TSTRING, 32822)
PR_EMS_AB_EXTENSION_ATTRIBUTE_10_A = PROP_TAG(PT_STRING8, 32822)
PR_EMS_AB_EXTENSION_ATTRIBUTE_10_W = PROP_TAG(PT_UNICODE, 32822)
PR_EMS_AB_EXTENSION_ATTRIBUTE_2 = PROP_TAG(PT_TSTRING, 32814)
PR_EMS_AB_EXTENSION_ATTRIBUTE_2_A = PROP_TAG(PT_STRING8, 32814)
PR_EMS_AB_EXTENSION_ATTRIBUTE_2_W = PROP_TAG(PT_UNICODE, 32814)
PR_EMS_AB_EXTENSION_ATTRIBUTE_3 = PROP_TAG(PT_TSTRING, 32815)
PR_EMS_AB_EXTENSION_ATTRIBUTE_3_A = PROP_TAG(PT_STRING8, 32815)
PR_EMS_AB_EXTENSION_ATTRIBUTE_3_W = PROP_TAG(PT_UNICODE, 32815)
PR_EMS_AB_EXTENSION_ATTRIBUTE_4 = PROP_TAG(PT_TSTRING, 32816)
PR_EMS_AB_EXTENSION_ATTRIBUTE_4_A = PROP_TAG(PT_STRING8, 32816)
PR_EMS_AB_EXTENSION_ATTRIBUTE_4_W = PROP_TAG(PT_UNICODE, 32816)
PR_EMS_AB_EXTENSION_ATTRIBUTE_5 = PROP_TAG(PT_TSTRING, 32817)
PR_EMS_AB_EXTENSION_ATTRIBUTE_5_A = PROP_TAG(PT_STRING8, 32817)
PR_EMS_AB_EXTENSION_ATTRIBUTE_5_W = PROP_TAG(PT_UNICODE, 32817)
PR_EMS_AB_EXTENSION_ATTRIBUTE_6 = PROP_TAG(PT_TSTRING, 32818)
PR_EMS_AB_EXTENSION_ATTRIBUTE_6_A = PROP_TAG(PT_STRING8, 32818)
PR_EMS_AB_EXTENSION_ATTRIBUTE_6_W = PROP_TAG(PT_UNICODE, 32818)
PR_EMS_AB_EXTENSION_ATTRIBUTE_7 = PROP_TAG(PT_TSTRING, 32819)
PR_EMS_AB_EXTENSION_ATTRIBUTE_7_A = PROP_TAG(PT_STRING8, 32819)
PR_EMS_AB_EXTENSION_ATTRIBUTE_7_W = PROP_TAG(PT_UNICODE, 32819)
PR_EMS_AB_EXTENSION_ATTRIBUTE_8 = PROP_TAG(PT_TSTRING, 32820)
PR_EMS_AB_EXTENSION_ATTRIBUTE_8_A = PROP_TAG(PT_STRING8, 32820)
PR_EMS_AB_EXTENSION_ATTRIBUTE_8_W = PROP_TAG(PT_UNICODE, 32820)
PR_EMS_AB_EXTENSION_ATTRIBUTE_9 = PROP_TAG(PT_TSTRING, 32821)
PR_EMS_AB_EXTENSION_ATTRIBUTE_9_A = PROP_TAG(PT_STRING8, 32821)
PR_EMS_AB_EXTENSION_ATTRIBUTE_9_W = PROP_TAG(PT_UNICODE, 32821)
PR_EMS_AB_EXTENSION_DATA = PROP_TAG(PT_MV_BINARY, 32936)
PR_EMS_AB_EXTENSION_NAME = PROP_TAG(PT_MV_TSTRING, 32937)
PR_EMS_AB_EXTENSION_NAME_A = PROP_TAG(PT_MV_STRING8, 32937)
PR_EMS_AB_EXTENSION_NAME_W = PROP_TAG(PT_MV_UNICODE, 32937)
PR_EMS_AB_EXTENSION_NAME_INHERITED = PROP_TAG(PT_MV_TSTRING, 32938)
PR_EMS_AB_EXTENSION_NAME_INHERITED_A = PROP_TAG(PT_MV_STRING8, 32938)
PR_EMS_AB_EXTENSION_NAME_INHERITED_W = PROP_TAG(PT_MV_UNICODE, 32938)
PR_EMS_AB_FACSIMILE_TELEPHONE_NUMBER = PROP_TAG(PT_MV_BINARY, 32939)
PR_EMS_AB_FILE_VERSION = PROP_TAG(PT_BINARY, 32940)
PR_EMS_AB_FILTER_LOCAL_ADDRESSES = PROP_TAG(PT_BOOLEAN, 32941)
PR_EMS_AB_FOLDER_PATHNAME = PROP_TAG(PT_TSTRING, 32772)
PR_EMS_AB_FOLDER_PATHNAME_A = PROP_TAG(PT_STRING8, 32772)
PR_EMS_AB_FOLDER_PATHNAME_W = PROP_TAG(PT_UNICODE, 32772)
PR_EMS_AB_FOLDERS_CONTAINER = PROP_TAG(PT_TSTRING, 32942)
PR_EMS_AB_FOLDERS_CONTAINER_A = PROP_TAG(PT_STRING8, 32942)
PR_EMS_AB_FOLDERS_CONTAINER_W = PROP_TAG(PT_UNICODE, 32942)
PR_EMS_AB_FOLDERS_CONTAINER_O = PROP_TAG(PT_OBJECT, 32942)
PR_EMS_AB_FOLDERS_CONTAINER_T = PROP_TAG(PT_TSTRING, 32942)
PR_EMS_AB_GARBAGE_COLL_PERIOD = PROP_TAG(PT_LONG, 32943)
PR_EMS_AB_GATEWAY_LOCAL_CRED = PROP_TAG(PT_TSTRING, 32944)
PR_EMS_AB_GATEWAY_LOCAL_CRED_A = PROP_TAG(PT_STRING8, 32944)
PR_EMS_AB_GATEWAY_LOCAL_CRED_W = PROP_TAG(PT_UNICODE, 32944)
PR_EMS_AB_GATEWAY_LOCAL_DESIG = PROP_TAG(PT_TSTRING, 32945)
PR_EMS_AB_GATEWAY_LOCAL_DESIG_A = PROP_TAG(PT_STRING8, 32945)
PR_EMS_AB_GATEWAY_LOCAL_DESIG_W = PROP_TAG(PT_UNICODE, 32945)
PR_EMS_AB_GATEWAY_PROXY = PROP_TAG(PT_MV_TSTRING, 32946)
PR_EMS_AB_GATEWAY_PROXY_A = PROP_TAG(PT_MV_STRING8, 32946)
PR_EMS_AB_GATEWAY_PROXY_W = PROP_TAG(PT_MV_UNICODE, 32946)
PR_EMS_AB_GATEWAY_ROUTING_TREE = PROP_TAG(PT_BINARY, 32947)
PR_EMS_AB_GWART_LAST_MODIFIED = PROP_TAG(PT_SYSTIME, 32948)
PR_EMS_AB_HAS_FULL_REPLICA_NCS = PROP_TAG(PT_MV_TSTRING, 32949)
PR_EMS_AB_HAS_FULL_REPLICA_NCS_A = PROP_TAG(PT_MV_STRING8, 32949)
PR_EMS_AB_HAS_FULL_REPLICA_NCS_W = PROP_TAG(PT_MV_UNICODE, 32949)
PR_EMS_AB_HAS_FULL_REPLICA_NCS_O = PROP_TAG(PT_OBJECT, 32949)
PR_EMS_AB_HAS_FULL_REPLICA_NCS_T = PROP_TAG(PT_MV_TSTRING, 32949)
PR_EMS_AB_HAS_MASTER_NCS = PROP_TAG(PT_MV_TSTRING, 32950)
PR_EMS_AB_HAS_MASTER_NCS_A = PROP_TAG(PT_MV_STRING8, 32950)
PR_EMS_AB_HAS_MASTER_NCS_W = PROP_TAG(PT_MV_UNICODE, 32950)
PR_EMS_AB_HAS_MASTER_NCS_O = PROP_TAG(PT_OBJECT, 32950)
PR_EMS_AB_HAS_MASTER_NCS_T = PROP_TAG(PT_MV_TSTRING, 32950)
PR_EMS_AB_HELP_DATA16 = PROP_TAG(PT_BINARY, 32826)
PR_EMS_AB_HELP_DATA32 = PROP_TAG(PT_BINARY, 32784)
PR_EMS_AB_HELP_FILE_NAME = PROP_TAG(PT_TSTRING, 32827)
PR_EMS_AB_HELP_FILE_NAME_A = PROP_TAG(PT_STRING8, 32827)
PR_EMS_AB_HELP_FILE_NAME_W = PROP_TAG(PT_UNICODE, 32827)
PR_EMS_AB_HEURISTICS = PROP_TAG(PT_LONG, 32951)
PR_EMS_AB_HIDE_DL_MEMBERSHIP = PROP_TAG(PT_BOOLEAN, 32952)
PR_EMS_AB_HIDE_FROM_ADDRESS_BOOK = PROP_TAG(PT_BOOLEAN, 32953)
PR_EMS_AB_HOME_MDB = PROP_TAG(PT_TSTRING, 32774)
PR_EMS_AB_HOME_MDB_A = PROP_TAG(PT_STRING8, 32774)
PR_EMS_AB_HOME_MDB_W = PROP_TAG(PT_UNICODE, 32774)
PR_EMS_AB_HOME_MDB_O = PROP_TAG(PT_OBJECT, 32774)
PR_EMS_AB_HOME_MDB_T = PROP_TAG(PT_TSTRING, 32774)
PR_EMS_AB_HOME_MDB_BL = PROP_TAG(PT_MV_TSTRING, 32788)
PR_EMS_AB_HOME_MDB_BL_A = PROP_TAG(PT_MV_STRING8, 32788)
PR_EMS_AB_HOME_MDB_BL_W = PROP_TAG(PT_MV_UNICODE, 32788)
PR_EMS_AB_HOME_MDB_BL_O = PROP_TAG(PT_OBJECT, 32788)
PR_EMS_AB_HOME_MDB_BL_T = PROP_TAG(PT_MV_TSTRING, 32788)
PR_EMS_AB_HOME_MTA = PROP_TAG(PT_TSTRING, 32775)
PR_EMS_AB_HOME_MTA_A = PROP_TAG(PT_STRING8, 32775)
PR_EMS_AB_HOME_MTA_W = PROP_TAG(PT_UNICODE, 32775)
PR_EMS_AB_HOME_MTA_O = PROP_TAG(PT_OBJECT, 32775)
PR_EMS_AB_HOME_MTA_T = PROP_TAG(PT_TSTRING, 32775)
PR_EMS_AB_HOME_PUBLIC_SERVER = PROP_TAG(PT_TSTRING, 32831)
PR_EMS_AB_HOME_PUBLIC_SERVER_A = PROP_TAG(PT_STRING8, 32831)
PR_EMS_AB_HOME_PUBLIC_SERVER_W = PROP_TAG(PT_UNICODE, 32831)
PR_EMS_AB_HOME_PUBLIC_SERVER_O = PROP_TAG(PT_OBJECT, 32831)
PR_EMS_AB_HOME_PUBLIC_SERVER_T = PROP_TAG(PT_TSTRING, 32831)
PR_EMS_AB_IMPORT_CONTAINER = PROP_TAG(PT_TSTRING, 32954)
PR_EMS_AB_IMPORT_CONTAINER_A = PROP_TAG(PT_STRING8, 32954)
PR_EMS_AB_IMPORT_CONTAINER_W = PROP_TAG(PT_UNICODE, 32954)
PR_EMS_AB_IMPORT_CONTAINER_O = PROP_TAG(PT_OBJECT, 32954)
PR_EMS_AB_IMPORT_CONTAINER_T = PROP_TAG(PT_TSTRING, 32954)
PR_EMS_AB_IMPORT_SENSITIVITY = PROP_TAG(PT_LONG, 32955)
PR_EMS_AB_IMPORTED_FROM = PROP_TAG(PT_TSTRING, 32834)
PR_EMS_AB_IMPORTED_FROM_A = PROP_TAG(PT_STRING8, 32834)
PR_EMS_AB_IMPORTED_FROM_W = PROP_TAG(PT_UNICODE, 32834)
PR_EMS_AB_INBOUND_SITES = PROP_TAG(PT_MV_TSTRING, 32956)
PR_EMS_AB_INBOUND_SITES_A = PROP_TAG(PT_MV_STRING8, 32956)
PR_EMS_AB_INBOUND_SITES_W = PROP_TAG(PT_MV_UNICODE, 32956)
PR_EMS_AB_INBOUND_SITES_O = PROP_TAG(PT_OBJECT, 32956)
PR_EMS_AB_INBOUND_SITES_T = PROP_TAG(PT_MV_TSTRING, 32956)
PR_EMS_AB_INSTANCE_TYPE = PROP_TAG(PT_LONG, 32957)
PR_EMS_AB_INTERNATIONAL_ISDN_NUMBER = PROP_TAG(PT_MV_TSTRING, 32958)
PR_EMS_AB_INTERNATIONAL_ISDN_NUMBER_A = PROP_TAG(PT_MV_STRING8, 32958)
PR_EMS_AB_INTERNATIONAL_ISDN_NUMBER_W = PROP_TAG(PT_MV_UNICODE, 32958)
PR_EMS_AB_INVOCATION_ID = PROP_TAG(PT_BINARY, 32959)
PR_EMS_AB_IS_DELETED = PROP_TAG(PT_BOOLEAN, 32960)
PR_EMS_AB_IS_MEMBER_OF_DL = PROP_TAG(PT_OBJECT, 32776)
PR_EMS_AB_IS_MEMBER_OF_DL_A = PROP_TAG(PT_MV_STRING8, 32776)
PR_EMS_AB_IS_MEMBER_OF_DL_W = PROP_TAG(PT_MV_UNICODE, 32776)
PR_EMS_AB_IS_MEMBER_OF_DL_O = PROP_TAG(PT_OBJECT, 32776)
PR_EMS_AB_IS_MEMBER_OF_DL_T = PROP_TAG(PT_MV_TSTRING, 32776)
PR_EMS_AB_IS_SINGLE_VALUED = PROP_TAG(PT_BOOLEAN, 32961)
PR_EMS_AB_KCC_STATUS = PROP_TAG(PT_MV_BINARY, 32962)
PR_EMS_AB_KM_SERVER = PROP_TAG(PT_TSTRING, 32781)
PR_EMS_AB_KM_SERVER_A = PROP_TAG(PT_STRING8, 32781)
PR_EMS_AB_KM_SERVER_W = PROP_TAG(PT_UNICODE, 32781)
PR_EMS_AB_KM_SERVER_O = PROP_TAG(PT_OBJECT, 32781)
PR_EMS_AB_KM_SERVER_T = PROP_TAG(PT_TSTRING, 32781)
PR_EMS_AB_KNOWLEDGE_INFORMATION = PROP_TAG(PT_MV_TSTRING, 32963)
PR_EMS_AB_KNOWLEDGE_INFORMATION_A = PROP_TAG(PT_MV_STRING8, 32963)
PR_EMS_AB_KNOWLEDGE_INFORMATION_W = PROP_TAG(PT_MV_UNICODE, 32963)
PR_EMS_AB_LANGUAGE = PROP_TAG(PT_LONG, 33144)
PR_EMS_AB_LDAP_DISPLAY_NAME = PROP_TAG(PT_MV_TSTRING, 33137)
PR_EMS_AB_LDAP_DISPLAY_NAME_A = PROP_TAG(PT_MV_STRING8, 33137)
PR_EMS_AB_LDAP_DISPLAY_NAME_W = PROP_TAG(PT_MV_UNICODE, 33137)
PR_EMS_AB_LINE_WRAP = PROP_TAG(PT_LONG, 32964)
PR_EMS_AB_LINK_ID = PROP_TAG(PT_LONG, 32965)
PR_EMS_AB_LOCAL_BRIDGE_HEAD = PROP_TAG(PT_TSTRING, 32966)
PR_EMS_AB_LOCAL_BRIDGE_HEAD_A = PROP_TAG(PT_STRING8, 32966)
PR_EMS_AB_LOCAL_BRIDGE_HEAD_W = PROP_TAG(PT_UNICODE, 32966)
PR_EMS_AB_LOCAL_BRIDGE_HEAD_ADDRESS = PROP_TAG(PT_TSTRING, 32967)
PR_EMS_AB_LOCAL_BRIDGE_HEAD_ADDRESS_A = PROP_TAG(PT_STRING8, 32967)
PR_EMS_AB_LOCAL_BRIDGE_HEAD_ADDRESS_W = PROP_TAG(PT_UNICODE, 32967)
PR_EMS_AB_LOCAL_INITIAL_TURN = PROP_TAG(PT_BOOLEAN, 32968)
PR_EMS_AB_LOCAL_SCOPE = PROP_TAG(PT_MV_TSTRING, 32969)
PR_EMS_AB_LOCAL_SCOPE_A = PROP_TAG(PT_MV_STRING8, 32969)
PR_EMS_AB_LOCAL_SCOPE_W = PROP_TAG(PT_MV_UNICODE, 32969)
PR_EMS_AB_LOCAL_SCOPE_O = PROP_TAG(PT_OBJECT, 32969)
PR_EMS_AB_LOCAL_SCOPE_T = PROP_TAG(PT_MV_TSTRING, 32969)
PR_EMS_AB_LOG_FILENAME = PROP_TAG(PT_TSTRING, 32970)
PR_EMS_AB_LOG_FILENAME_A = PROP_TAG(PT_STRING8, 32970)
PR_EMS_AB_LOG_FILENAME_W = PROP_TAG(PT_UNICODE, 32970)
PR_EMS_AB_LOG_ROLLOVER_INTERVAL = PROP_TAG(PT_LONG, 32971)
PR_EMS_AB_MAINTAIN_AUTOREPLY_HISTORY = PROP_TAG(PT_BOOLEAN, 32972)
PR_EMS_AB_MANAGER = PROP_TAG(PT_OBJECT, 32773)
PR_EMS_AB_MANAGER_A = PROP_TAG(PT_STRING8, 32773)
PR_EMS_AB_MANAGER_W = PROP_TAG(PT_UNICODE, 32773)
PR_EMS_AB_MANAGER_O = PROP_TAG(PT_OBJECT, 32773)
PR_EMS_AB_MANAGER_T = PROP_TAG(PT_TSTRING, 32773)
PR_EMS_AB_MAPI_DISPLAY_TYPE = PROP_TAG(PT_LONG, 32973)
PR_EMS_AB_MAPI_ID = PROP_TAG(PT_LONG, 32974)
PR_EMS_AB_MAXIMUM_OBJECT_ID = PROP_TAG(PT_BINARY, 33129)
PR_EMS_AB_MDB_BACKOFF_INTERVAL = PROP_TAG(PT_LONG, 32975)
PR_EMS_AB_MDB_MSG_TIME_OUT_PERIOD = PROP_TAG(PT_LONG, 32976)
PR_EMS_AB_MDB_OVER_QUOTA_LIMIT = PROP_TAG(PT_LONG, 32977)
PR_EMS_AB_MDB_STORAGE_QUOTA = PROP_TAG(PT_LONG, 32978)
PR_EMS_AB_MDB_UNREAD_LIMIT = PROP_TAG(PT_LONG, 32979)
PR_EMS_AB_MDB_USE_DEFAULTS = PROP_TAG(PT_BOOLEAN, 32980)
PR_EMS_AB_MEMBER = PROP_TAG(PT_OBJECT, 32777)
PR_EMS_AB_MEMBER_A = PROP_TAG(PT_MV_STRING8, 32777)
PR_EMS_AB_MEMBER_W = PROP_TAG(PT_MV_UNICODE, 32777)
PR_EMS_AB_MEMBER_O = PROP_TAG(PT_OBJECT, 32777)
PR_EMS_AB_MEMBER_T = PROP_TAG(PT_MV_TSTRING, 32777)
PR_EMS_AB_MESSAGE_TRACKING_ENABLED = PROP_TAG(PT_BOOLEAN, 32981)
PR_EMS_AB_MONITOR_CLOCK = PROP_TAG(PT_BOOLEAN, 32982)
PR_EMS_AB_MONITOR_SERVERS = PROP_TAG(PT_BOOLEAN, 32983)
PR_EMS_AB_MONITOR_SERVICES = PROP_TAG(PT_BOOLEAN, 32984)
PR_EMS_AB_MONITORED_CONFIGURATIONS = PROP_TAG(PT_MV_TSTRING, 32985)
PR_EMS_AB_MONITORED_CONFIGURATIONS_A = PROP_TAG(PT_MV_STRING8, 32985)
PR_EMS_AB_MONITORED_CONFIGURATIONS_W = PROP_TAG(PT_MV_UNICODE, 32985)
PR_EMS_AB_MONITORED_CONFIGURATIONS_O = PROP_TAG(PT_OBJECT, 32985)
PR_EMS_AB_MONITORED_CONFIGURATIONS_T = PROP_TAG(PT_MV_TSTRING, 32985)
PR_EMS_AB_MONITORED_SERVERS = PROP_TAG(PT_MV_TSTRING, 32986)
PR_EMS_AB_MONITORED_SERVERS_A = PROP_TAG(PT_MV_STRING8, 32986)
PR_EMS_AB_MONITORED_SERVERS_W = PROP_TAG(PT_MV_UNICODE, 32986)
PR_EMS_AB_MONITORED_SERVERS_O = PROP_TAG(PT_OBJECT, 32986)
PR_EMS_AB_MONITORED_SERVERS_T = PROP_TAG(PT_MV_TSTRING, 32986)
PR_EMS_AB_MONITORED_SERVICES = PROP_TAG(PT_MV_TSTRING, 32987)
PR_EMS_AB_MONITORED_SERVICES_A = PROP_TAG(PT_MV_STRING8, 32987)
PR_EMS_AB_MONITORED_SERVICES_W = PROP_TAG(PT_MV_UNICODE, 32987)
PR_EMS_AB_MONITORING_ALERT_DELAY = PROP_TAG(PT_LONG, 32988)
PR_EMS_AB_MONITORING_ALERT_UNITS = PROP_TAG(PT_LONG, 32989)
PR_EMS_AB_MONITORING_AVAILABILITY_STYLE = PROP_TAG(PT_LONG, 32990)
PR_EMS_AB_MONITORING_AVAILABILITY_WINDOW = PROP_TAG(PT_BINARY, 32991)
PR_EMS_AB_MONITORING_CACHED_VIA_MAIL = PROP_TAG(PT_MV_TSTRING, 32992)
PR_EMS_AB_MONITORING_CACHED_VIA_MAIL_A = PROP_TAG(PT_MV_STRING8, 32992)
PR_EMS_AB_MONITORING_CACHED_VIA_MAIL_W = PROP_TAG(PT_MV_UNICODE, 32992)
PR_EMS_AB_MONITORING_CACHED_VIA_MAIL_O = PROP_TAG(PT_OBJECT, 32992)
PR_EMS_AB_MONITORING_CACHED_VIA_MAIL_T = PROP_TAG(PT_MV_TSTRING, 32992)
PR_EMS_AB_MONITORING_CACHED_VIA_RPC = PROP_TAG(PT_MV_TSTRING, 32993)
PR_EMS_AB_MONITORING_CACHED_VIA_RPC_A = PROP_TAG(PT_MV_STRING8, 32993)
PR_EMS_AB_MONITORING_CACHED_VIA_RPC_W = PROP_TAG(PT_MV_UNICODE, 32993)
PR_EMS_AB_MONITORING_CACHED_VIA_RPC_O = PROP_TAG(PT_OBJECT, 32993)
PR_EMS_AB_MONITORING_CACHED_VIA_RPC_T = PROP_TAG(PT_MV_TSTRING, 32993)
PR_EMS_AB_MONITORING_ESCALATION_PROCEDURE = PROP_TAG(PT_MV_BINARY, 32994)
PR_EMS_AB_MONITORING_HOTSITE_POLL_INTERVAL = PROP_TAG(PT_LONG, 32995)
PR_EMS_AB_MONITORING_HOTSITE_POLL_UNITS = PROP_TAG(PT_LONG, 32996)
PR_EMS_AB_MONITORING_MAIL_UPDATE_INTERVAL = PROP_TAG(PT_LONG, 32997)
PR_EMS_AB_MONITORING_MAIL_UPDATE_UNITS = PROP_TAG(PT_LONG, 32998)
PR_EMS_AB_MONITORING_NORMAL_POLL_INTERVAL = PROP_TAG(PT_LONG, 32999)
PR_EMS_AB_MONITORING_NORMAL_POLL_UNITS = PROP_TAG(PT_LONG, 33000)
PR_EMS_AB_MONITORING_RECIPIENTS = PROP_TAG(PT_MV_TSTRING, 33001)
PR_EMS_AB_MONITORING_RECIPIENTS_A = PROP_TAG(PT_MV_STRING8, 33001)
PR_EMS_AB_MONITORING_RECIPIENTS_W = PROP_TAG(PT_MV_UNICODE, 33001)
PR_EMS_AB_MONITORING_RECIPIENTS_O = PROP_TAG(PT_OBJECT, 33001)
PR_EMS_AB_MONITORING_RECIPIENTS_T = PROP_TAG(PT_MV_TSTRING, 33001)
PR_EMS_AB_MONITORING_RECIPIENTS_NDR = PROP_TAG(PT_MV_TSTRING, 33002)
PR_EMS_AB_MONITORING_RECIPIENTS_NDR_A = PROP_TAG(PT_MV_STRING8, 33002)
PR_EMS_AB_MONITORING_RECIPIENTS_NDR_W = PROP_TAG(PT_MV_UNICODE, 33002)
PR_EMS_AB_MONITORING_RECIPIENTS_NDR_O = PROP_TAG(PT_OBJECT, 33002)
PR_EMS_AB_MONITORING_RECIPIENTS_NDR_T = PROP_TAG(PT_MV_TSTRING, 33002)
PR_EMS_AB_MONITORING_RPC_UPDATE_INTERVAL = PROP_TAG(PT_LONG, 33003)
PR_EMS_AB_MONITORING_RPC_UPDATE_UNITS = PROP_TAG(PT_LONG, 33004)
PR_EMS_AB_MONITORING_WARNING_DELAY = PROP_TAG(PT_LONG, 33005)
PR_EMS_AB_MONITORING_WARNING_UNITS = PROP_TAG(PT_LONG, 33006)
PR_EMS_AB_MTA_LOCAL_CRED = PROP_TAG(PT_TSTRING, 33007)
PR_EMS_AB_MTA_LOCAL_CRED_A = PROP_TAG(PT_STRING8, 33007)
PR_EMS_AB_MTA_LOCAL_CRED_W = PROP_TAG(PT_UNICODE, 33007)
PR_EMS_AB_MTA_LOCAL_DESIG = PROP_TAG(PT_TSTRING, 33008)
PR_EMS_AB_MTA_LOCAL_DESIG_A = PROP_TAG(PT_STRING8, 33008)
PR_EMS_AB_MTA_LOCAL_DESIG_W = PROP_TAG(PT_UNICODE, 33008)
PR_EMS_AB_N_ADDRESS = PROP_TAG(PT_BINARY, 33009)
PR_EMS_AB_N_ADDRESS_TYPE = PROP_TAG(PT_LONG, 33010)
PR_EMS_AB_NETWORK_ADDRESS = PROP_TAG(PT_MV_TSTRING, 33136)
PR_EMS_AB_NETWORK_ADDRESS_A = PROP_TAG(PT_MV_STRING8, 33136)
PR_EMS_AB_NETWORK_ADDRESS_W = PROP_TAG(PT_MV_UNICODE, 33136)
PR_EMS_AB_NNTP_CHARACTER_SET = PROP_TAG(PT_TSTRING, 33149)
PR_EMS_AB_NNTP_CHARACTER_SET_A = PROP_TAG(PT_STRING8, 33149)
PR_EMS_AB_NNTP_CHARACTER_SET_W = PROP_TAG(PT_UNICODE, 33149)
PR_EMS_AB_NNTP_CONTENT_FORMAT = PROP_TAG(PT_TSTRING, 33142)
PR_EMS_AB_NNTP_CONTENT_FORMAT_A = PROP_TAG(PT_STRING8, 33142)
PR_EMS_AB_NNTP_CONTENT_FORMAT_W = PROP_TAG(PT_UNICODE, 33142)
PR_EMS_AB_NT_MACHINE_NAME = PROP_TAG(PT_TSTRING, 33011)
PR_EMS_AB_NT_MACHINE_NAME_A = PROP_TAG(PT_STRING8, 33011)
PR_EMS_AB_NT_MACHINE_NAME_W = PROP_TAG(PT_UNICODE, 33011)
PR_EMS_AB_NT_SECURITY_DESCRIPTOR = PROP_TAG(PT_BINARY, 32787)
PR_EMS_AB_NUM_OF_OPEN_RETRIES = PROP_TAG(PT_LONG, 33012)
PR_EMS_AB_NUM_OF_TRANSFER_RETRIES = PROP_TAG(PT_LONG, 33013)
PR_EMS_AB_OBJ_DIST_NAME = PROP_TAG(PT_TSTRING, 32828)
PR_EMS_AB_OBJ_DIST_NAME_A = PROP_TAG(PT_STRING8, 32828)
PR_EMS_AB_OBJ_DIST_NAME_W = PROP_TAG(PT_UNICODE, 32828)
PR_EMS_AB_OBJ_DIST_NAME_O = PROP_TAG(PT_OBJECT, 32828)
PR_EMS_AB_OBJ_DIST_NAME_T = PROP_TAG(PT_TSTRING, 32828)
PR_EMS_AB_OBJECT_CLASS_CATEGORY = PROP_TAG(PT_LONG, 33014)
PR_EMS_AB_OBJECT_VERSION = PROP_TAG(PT_LONG, 33015)
PR_EMS_AB_OFF_LINE_AB_CONTAINERS = PROP_TAG(PT_MV_TSTRING, 33016)
PR_EMS_AB_OFF_LINE_AB_CONTAINERS_A = PROP_TAG(PT_MV_STRING8, 33016)
PR_EMS_AB_OFF_LINE_AB_CONTAINERS_W = PROP_TAG(PT_MV_UNICODE, 33016)
PR_EMS_AB_OFF_LINE_AB_CONTAINERS_O = PROP_TAG(PT_OBJECT, 33016)
PR_EMS_AB_OFF_LINE_AB_CONTAINERS_T = PROP_TAG(PT_MV_TSTRING, 33016)
PR_EMS_AB_OFF_LINE_AB_SCHEDULE = PROP_TAG(PT_BINARY, 33017)
PR_EMS_AB_OFF_LINE_AB_SERVER = PROP_TAG(PT_TSTRING, 33018)
PR_EMS_AB_OFF_LINE_AB_SERVER_A = PROP_TAG(PT_STRING8, 33018)
PR_EMS_AB_OFF_LINE_AB_SERVER_W = PROP_TAG(PT_UNICODE, 33018)
PR_EMS_AB_OFF_LINE_AB_SERVER_O = PROP_TAG(PT_OBJECT, 33018)
PR_EMS_AB_OFF_LINE_AB_SERVER_T = PROP_TAG(PT_TSTRING, 33018)
PR_EMS_AB_OFF_LINE_AB_STYLE = PROP_TAG(PT_LONG, 33019)
PR_EMS_AB_OID_TYPE = PROP_TAG(PT_LONG, 33020)
PR_EMS_AB_OM_OBJECT_CLASS = PROP_TAG(PT_BINARY, 33021)
PR_EMS_AB_OM_SYNTAX = PROP_TAG(PT_LONG, 33022)
PR_EMS_AB_OOF_REPLY_TO_ORIGINATOR = PROP_TAG(PT_BOOLEAN, 33023)
PR_EMS_AB_OPEN_RETRY_INTERVAL = PROP_TAG(PT_LONG, 33024)
PR_EMS_AB_ORGANIZATION_NAME = PROP_TAG(PT_MV_TSTRING, 33025)
PR_EMS_AB_ORGANIZATION_NAME_A = PROP_TAG(PT_MV_STRING8, 33025)
PR_EMS_AB_ORGANIZATION_NAME_W = PROP_TAG(PT_MV_UNICODE, 33025)
PR_EMS_AB_ORGANIZATIONAL_UNIT_NAME = PROP_TAG(PT_MV_TSTRING, 33026)
PR_EMS_AB_ORGANIZATIONAL_UNIT_NAME_A = PROP_TAG(PT_MV_STRING8, 33026)
PR_EMS_AB_ORGANIZATIONAL_UNIT_NAME_W = PROP_TAG(PT_MV_UNICODE, 33026)
PR_EMS_AB_ORIGINAL_DISPLAY_TABLE = PROP_TAG(PT_BINARY, 33027)
PR_EMS_AB_ORIGINAL_DISPLAY_TABLE_MSDOS = PROP_TAG(PT_BINARY, 33028)
PR_EMS_AB_OUTBOUND_SITES = PROP_TAG(PT_MV_TSTRING, 33029)
PR_EMS_AB_OUTBOUND_SITES_A = PROP_TAG(PT_MV_STRING8, 33029)
PR_EMS_AB_OUTBOUND_SITES_W = PROP_TAG(PT_MV_UNICODE, 33029)
PR_EMS_AB_OUTBOUND_SITES_O = PROP_TAG(PT_OBJECT, 33029)
PR_EMS_AB_OUTBOUND_SITES_T = PROP_TAG(PT_MV_TSTRING, 33029)
PR_EMS_AB_OWNER = PROP_TAG(PT_TSTRING, 32780)
PR_EMS_AB_OWNER_A = PROP_TAG(PT_STRING8, 32780)
PR_EMS_AB_OWNER_W = PROP_TAG(PT_UNICODE, 32780)
PR_EMS_AB_OWNER_O = PROP_TAG(PT_OBJECT, 32780)
PR_EMS_AB_OWNER_T = PROP_TAG(PT_TSTRING, 32780)
PR_EMS_AB_OWNER_BL = PROP_TAG(PT_TSTRING, 32804)
PR_EMS_AB_OWNER_BL_A = PROP_TAG(PT_STRING8, 32804)
PR_EMS_AB_OWNER_BL_W = PROP_TAG(PT_UNICODE, 32804)
PR_EMS_AB_OWNER_BL_O = PROP_TAG(PT_OBJECT, 32804)
PR_EMS_AB_OWNER_BL_T = PROP_TAG(PT_TSTRING, 32804)
PR_EMS_AB_P_SELECTOR = PROP_TAG(PT_BINARY, 33030)
PR_EMS_AB_P_SELECTOR_INBOUND = PROP_TAG(PT_BINARY, 33031)
PR_EMS_AB_PER_MSG_DIALOG_DISPLAY_TABLE = PROP_TAG(PT_BINARY, 33032)
PR_EMS_AB_PER_RECIP_DIALOG_DISPLAY_TABLE = PROP_TAG(PT_BINARY, 33033)
PR_EMS_AB_PERIOD_REP_SYNC_TIMES = PROP_TAG(PT_BINARY, 33034)
PR_EMS_AB_PERIOD_REPL_STAGGER = PROP_TAG(PT_LONG, 33035)
PR_EMS_AB_PF_CONTACTS = PROP_TAG(PT_MV_TSTRING, 32824)
PR_EMS_AB_PF_CONTACTS_A = PROP_TAG(PT_MV_STRING8, 32824)
PR_EMS_AB_PF_CONTACTS_W = PROP_TAG(PT_MV_UNICODE, 32824)
PR_EMS_AB_PF_CONTACTS_O = PROP_TAG(PT_OBJECT, 32824)
PR_EMS_AB_PF_CONTACTS_T = PROP_TAG(PT_MV_TSTRING, 32824)
PR_EMS_AB_POP_CHARACTER_SET = PROP_TAG(PT_TSTRING, 33145)
PR_EMS_AB_POP_CHARACTER_SET_A = PROP_TAG(PT_STRING8, 33145)
PR_EMS_AB_POP_CHARACTER_SET_W = PROP_TAG(PT_UNICODE, 33145)
PR_EMS_AB_POP_CONTENT_FORMAT = PROP_TAG(PT_TSTRING, 33143)
PR_EMS_AB_POP_CONTENT_FORMAT_A = PROP_TAG(PT_STRING8, 33143)
PR_EMS_AB_POP_CONTENT_FORMAT_W = PROP_TAG(PT_UNICODE, 33143)
PR_EMS_AB_POSTAL_ADDRESS = PROP_TAG(PT_MV_BINARY, 33036)
PR_EMS_AB_PREFERRED_DELIVERY_METHOD = PROP_TAG(PT_MV_LONG, 33037)
PR_EMS_AB_PRMD = PROP_TAG(PT_TSTRING, 33038)
PR_EMS_AB_PRMD_A = PROP_TAG(PT_STRING8, 33038)
PR_EMS_AB_PRMD_W = PROP_TAG(PT_UNICODE, 33038)
PR_EMS_AB_PROXY_ADDRESSES = PROP_TAG(PT_MV_TSTRING, 32783)
PR_EMS_AB_PROXY_ADDRESSES_A = PROP_TAG(PT_MV_STRING8, 32783)
PR_EMS_AB_PROXY_ADDRESSES_W = PROP_TAG(PT_MV_UNICODE, 32783)
PR_EMS_AB_PROXY_GENERATOR_DLL = PROP_TAG(PT_TSTRING, 33039)
PR_EMS_AB_PROXY_GENERATOR_DLL_A = PROP_TAG(PT_STRING8, 33039)
PR_EMS_AB_PROXY_GENERATOR_DLL_W = PROP_TAG(PT_UNICODE, 33039)
PR_EMS_AB_PUBLIC_DELEGATES = PROP_TAG(PT_OBJECT, 32789)
PR_EMS_AB_PUBLIC_DELEGATES_A = PROP_TAG(PT_MV_STRING8, 32789)
PR_EMS_AB_PUBLIC_DELEGATES_W = PROP_TAG(PT_MV_UNICODE, 32789)
PR_EMS_AB_PUBLIC_DELEGATES_O = PROP_TAG(PT_OBJECT, 32789)
PR_EMS_AB_PUBLIC_DELEGATES_T = PROP_TAG(PT_MV_TSTRING, 32789)
PR_EMS_AB_PUBLIC_DELEGATES_BL = PROP_TAG(PT_MV_TSTRING, 33040)
PR_EMS_AB_PUBLIC_DELEGATES_BL_A = PROP_TAG(PT_MV_STRING8, 33040)
PR_EMS_AB_PUBLIC_DELEGATES_BL_W = PROP_TAG(PT_MV_UNICODE, 33040)
PR_EMS_AB_PUBLIC_DELEGATES_BL_O = PROP_TAG(PT_OBJECT, 33040)
PR_EMS_AB_PUBLIC_DELEGATES_BL_T = PROP_TAG(PT_MV_TSTRING, 33040)
PR_EMS_AB_QUOTA_NOTIFICATION_SCHEDULE = PROP_TAG(PT_BINARY, 33041)
PR_EMS_AB_QUOTA_NOTIFICATION_STYLE = PROP_TAG(PT_LONG, 33042)
PR_EMS_AB_RANGE_LOWER = PROP_TAG(PT_LONG, 33043)
PR_EMS_AB_RANGE_UPPER = PROP_TAG(PT_LONG, 33044)
PR_EMS_AB_RAS_CALLBACK_NUMBER = PROP_TAG(PT_TSTRING, 33045)
PR_EMS_AB_RAS_CALLBACK_NUMBER_A = PROP_TAG(PT_STRING8, 33045)
PR_EMS_AB_RAS_CALLBACK_NUMBER_W = PROP_TAG(PT_UNICODE, 33045)
PR_EMS_AB_RAS_PHONE_NUMBER = PROP_TAG(PT_TSTRING, 33046)
PR_EMS_AB_RAS_PHONE_NUMBER_A = PROP_TAG(PT_STRING8, 33046)
PR_EMS_AB_RAS_PHONE_NUMBER_W = PROP_TAG(PT_UNICODE, 33046)
PR_EMS_AB_RAS_PHONEBOOK_ENTRY_NAME = PROP_TAG(PT_TSTRING, 33047)
PR_EMS_AB_RAS_PHONEBOOK_ENTRY_NAME_A = PROP_TAG(PT_STRING8, 33047)
PR_EMS_AB_RAS_PHONEBOOK_ENTRY_NAME_W = PROP_TAG(PT_UNICODE, 33047)
PR_EMS_AB_RAS_REMOTE_SRVR_NAME = PROP_TAG(PT_TSTRING, 33048)
PR_EMS_AB_RAS_REMOTE_SRVR_NAME_A = PROP_TAG(PT_STRING8, 33048)
PR_EMS_AB_RAS_REMOTE_SRVR_NAME_W = PROP_TAG(PT_UNICODE, 33048)
PR_EMS_AB_REGISTERED_ADDRESS = PROP_TAG(PT_MV_BINARY, 33049)
PR_EMS_AB_REMOTE_BRIDGE_HEAD = PROP_TAG(PT_TSTRING, 33050)
PR_EMS_AB_REMOTE_BRIDGE_HEAD_A = PROP_TAG(PT_STRING8, 33050)
PR_EMS_AB_REMOTE_BRIDGE_HEAD_W = PROP_TAG(PT_UNICODE, 33050)
PR_EMS_AB_REMOTE_BRIDGE_HEAD_ADDRESS = PROP_TAG(PT_TSTRING, 33051)
PR_EMS_AB_REMOTE_BRIDGE_HEAD_ADDRESS_A = PROP_TAG(PT_STRING8, 33051)
PR_EMS_AB_REMOTE_BRIDGE_HEAD_ADDRESS_W = PROP_TAG(PT_UNICODE, 33051)
PR_EMS_AB_REMOTE_OUT_BH_SERVER = PROP_TAG(PT_TSTRING, 33052)
PR_EMS_AB_REMOTE_OUT_BH_SERVER_A = PROP_TAG(PT_STRING8, 33052)
PR_EMS_AB_REMOTE_OUT_BH_SERVER_W = PROP_TAG(PT_UNICODE, 33052)
PR_EMS_AB_REMOTE_OUT_BH_SERVER_O = PROP_TAG(PT_OBJECT, 33052)
PR_EMS_AB_REMOTE_OUT_BH_SERVER_T = PROP_TAG(PT_TSTRING, 33052)
PR_EMS_AB_REMOTE_SITE = PROP_TAG(PT_TSTRING, 33053)
PR_EMS_AB_REMOTE_SITE_A = PROP_TAG(PT_STRING8, 33053)
PR_EMS_AB_REMOTE_SITE_W = PROP_TAG(PT_UNICODE, 33053)
PR_EMS_AB_REMOTE_SITE_O = PROP_TAG(PT_OBJECT, 33053)
PR_EMS_AB_REMOTE_SITE_T = PROP_TAG(PT_TSTRING, 33053)
PR_EMS_AB_REPLICATION_MAIL_MSG_SIZE = PROP_TAG(PT_LONG, 33128)
PR_EMS_AB_REPLICATION_SENSITIVITY = PROP_TAG(PT_LONG, 33054)
PR_EMS_AB_REPLICATION_STAGGER = PROP_TAG(PT_LONG, 33055)
PR_EMS_AB_REPORT_TO_ORIGINATOR = PROP_TAG(PT_BOOLEAN, 33056)
PR_EMS_AB_REPORT_TO_OWNER = PROP_TAG(PT_BOOLEAN, 33057)
PR_EMS_AB_REPORTS = PROP_TAG(PT_OBJECT, 32782)
PR_EMS_AB_REPORTS_A = PROP_TAG(PT_MV_STRING8, 32782)
PR_EMS_AB_REPORTS_W = PROP_TAG(PT_MV_UNICODE, 32782)
PR_EMS_AB_REPORTS_O = PROP_TAG(PT_OBJECT, 32782)
PR_EMS_AB_REPORTS_T = PROP_TAG(PT_MV_TSTRING, 32782)
PR_EMS_AB_REQ_SEQ = PROP_TAG(PT_LONG, 33058)
PR_EMS_AB_RESPONSIBLE_LOCAL_DXA = PROP_TAG(PT_TSTRING, 33059)
PR_EMS_AB_RESPONSIBLE_LOCAL_DXA_A = PROP_TAG(PT_STRING8, 33059)
PR_EMS_AB_RESPONSIBLE_LOCAL_DXA_W = PROP_TAG(PT_UNICODE, 33059)
PR_EMS_AB_RESPONSIBLE_LOCAL_DXA_O = PROP_TAG(PT_OBJECT, 33059)
PR_EMS_AB_RESPONSIBLE_LOCAL_DXA_T = PROP_TAG(PT_TSTRING, 33059)
PR_EMS_AB_RID_SERVER = PROP_TAG(PT_TSTRING, 33060)
PR_EMS_AB_RID_SERVER_A = PROP_TAG(PT_STRING8, 33060)
PR_EMS_AB_RID_SERVER_W = PROP_TAG(PT_UNICODE, 33060)
PR_EMS_AB_RID_SERVER_O = PROP_TAG(PT_OBJECT, 33060)
PR_EMS_AB_RID_SERVER_T = PROP_TAG(PT_TSTRING, 33060)
PR_EMS_AB_ROLE_OCCUPANT = PROP_TAG(PT_MV_TSTRING, 33061)
PR_EMS_AB_ROLE_OCCUPANT_A = PROP_TAG(PT_MV_STRING8, 33061)
PR_EMS_AB_ROLE_OCCUPANT_W = PROP_TAG(PT_MV_UNICODE, 33061)
PR_EMS_AB_ROLE_OCCUPANT_O = PROP_TAG(PT_OBJECT, 33061)
PR_EMS_AB_ROLE_OCCUPANT_T = PROP_TAG(PT_MV_TSTRING, 33061)
PR_EMS_AB_ROUTING_LIST = PROP_TAG(PT_MV_TSTRING, 33062)
PR_EMS_AB_ROUTING_LIST_A = PROP_TAG(PT_MV_STRING8, 33062)
PR_EMS_AB_ROUTING_LIST_W = PROP_TAG(PT_MV_UNICODE, 33062)
PR_EMS_AB_RTS_CHECKPOINT_SIZE = PROP_TAG(PT_LONG, 33063)
PR_EMS_AB_RTS_RECOVERY_TIMEOUT = PROP_TAG(PT_LONG, 33064)
PR_EMS_AB_RTS_WINDOW_SIZE = PROP_TAG(PT_LONG, 33065)
PR_EMS_AB_RUNS_ON = PROP_TAG(PT_MV_TSTRING, 33066)
PR_EMS_AB_RUNS_ON_A = PROP_TAG(PT_MV_STRING8, 33066)
PR_EMS_AB_RUNS_ON_W = PROP_TAG(PT_MV_UNICODE, 33066)
PR_EMS_AB_RUNS_ON_O = PROP_TAG(PT_OBJECT, 33066)
PR_EMS_AB_RUNS_ON_T = PROP_TAG(PT_MV_TSTRING, 33066)
PR_EMS_AB_S_SELECTOR = PROP_TAG(PT_BINARY, 33067)
PR_EMS_AB_S_SELECTOR_INBOUND = PROP_TAG(PT_BINARY, 33068)
PR_EMS_AB_SCHEMA_FLAGS = PROP_TAG(PT_LONG, 33139)
PR_EMS_AB_SCHEMA_VERSION = PROP_TAG(PT_MV_LONG, 33148)
PR_EMS_AB_SEARCH_FLAGS = PROP_TAG(PT_LONG, 33069)
PR_EMS_AB_SEARCH_GUIDE = PROP_TAG(PT_MV_BINARY, 33070)
PR_EMS_AB_SECURITY_PROTOCOL = PROP_TAG(PT_MV_BINARY, 32823)
PR_EMS_AB_SEE_ALSO = PROP_TAG(PT_MV_TSTRING, 33071)
PR_EMS_AB_SEE_ALSO_A = PROP_TAG(PT_MV_STRING8, 33071)
PR_EMS_AB_SEE_ALSO_W = PROP_TAG(PT_MV_UNICODE, 33071)
PR_EMS_AB_SEE_ALSO_O = PROP_TAG(PT_OBJECT, 33071)
PR_EMS_AB_SEE_ALSO_T = PROP_TAG(PT_MV_TSTRING, 33071)
PR_EMS_AB_SERIAL_NUMBER = PROP_TAG(PT_MV_TSTRING, 33072)
PR_EMS_AB_SERIAL_NUMBER_A = PROP_TAG(PT_MV_STRING8, 33072)
PR_EMS_AB_SERIAL_NUMBER_W = PROP_TAG(PT_MV_UNICODE, 33072)
PR_EMS_AB_SERVICE_ACTION_FIRST = PROP_TAG(PT_LONG, 33073)
PR_EMS_AB_SERVICE_ACTION_OTHER = PROP_TAG(PT_LONG, 33074)
PR_EMS_AB_SERVICE_ACTION_SECOND = PROP_TAG(PT_LONG, 33075)
PR_EMS_AB_SERVICE_RESTART_DELAY = PROP_TAG(PT_LONG, 33076)
PR_EMS_AB_SERVICE_RESTART_MESSAGE = PROP_TAG(PT_TSTRING, 33077)
PR_EMS_AB_SERVICE_RESTART_MESSAGE_A = PROP_TAG(PT_STRING8, 33077)
PR_EMS_AB_SERVICE_RESTART_MESSAGE_W = PROP_TAG(PT_UNICODE, 33077)
PR_EMS_AB_SESSION_DISCONNECT_TIMER = PROP_TAG(PT_LONG, 33078)
PR_EMS_AB_SITE_AFFINITY = PROP_TAG(PT_MV_TSTRING, 33079)
PR_EMS_AB_SITE_AFFINITY_A = PROP_TAG(PT_MV_STRING8, 33079)
PR_EMS_AB_SITE_AFFINITY_W = PROP_TAG(PT_MV_UNICODE, 33079)
PR_EMS_AB_SITE_FOLDER_GUID = PROP_TAG(PT_BINARY, 33126)
PR_EMS_AB_SITE_FOLDER_SERVER = PROP_TAG(PT_TSTRING, 33127)
PR_EMS_AB_SITE_FOLDER_SERVER_A = PROP_TAG(PT_STRING8, 33127)
PR_EMS_AB_SITE_FOLDER_SERVER_W = PROP_TAG(PT_UNICODE, 33127)
PR_EMS_AB_SITE_FOLDER_SERVER_O = PROP_TAG(PT_OBJECT, 33127)
PR_EMS_AB_SITE_FOLDER_SERVER_T = PROP_TAG(PT_TSTRING, 33127)
PR_EMS_AB_SITE_PROXY_SPACE = PROP_TAG(PT_MV_TSTRING, 33080)
PR_EMS_AB_SITE_PROXY_SPACE_A = PROP_TAG(PT_MV_STRING8, 33080)
PR_EMS_AB_SITE_PROXY_SPACE_W = PROP_TAG(PT_MV_UNICODE, 33080)
PR_EMS_AB_SPACE_LAST_COMPUTED = PROP_TAG(PT_SYSTIME, 33081)
PR_EMS_AB_STREET_ADDRESS = PROP_TAG(PT_TSTRING, 33082)
PR_EMS_AB_STREET_ADDRESS_A = PROP_TAG(PT_STRING8, 33082)
PR_EMS_AB_STREET_ADDRESS_W = PROP_TAG(PT_UNICODE, 33082)
PR_EMS_AB_SUB_REFS = PROP_TAG(PT_MV_TSTRING, 33083)
PR_EMS_AB_SUB_REFS_A = PROP_TAG(PT_MV_STRING8, 33083)
PR_EMS_AB_SUB_REFS_W = PROP_TAG(PT_MV_UNICODE, 33083)
PR_EMS_AB_SUB_REFS_O = PROP_TAG(PT_OBJECT, 33083)
PR_EMS_AB_SUB_REFS_T = PROP_TAG(PT_MV_TSTRING, 33083)
PR_EMS_AB_SUB_SITE = PROP_TAG(PT_TSTRING, 33147)
PR_EMS_AB_SUB_SITE_A = PROP_TAG(PT_STRING8, 33147)
PR_EMS_AB_SUB_SITE_W = PROP_TAG(PT_UNICODE, 33147)
PR_EMS_AB_SUBMISSION_CONT_LENGTH = PROP_TAG(PT_LONG, 33084)
PR_EMS_AB_SUPPORTED_APPLICATION_CONTEXT = PROP_TAG(PT_MV_BINARY, 33085)
PR_EMS_AB_SUPPORTING_STACK = PROP_TAG(PT_MV_TSTRING, 33086)
PR_EMS_AB_SUPPORTING_STACK_A = PROP_TAG(PT_MV_STRING8, 33086)
PR_EMS_AB_SUPPORTING_STACK_W = PROP_TAG(PT_MV_UNICODE, 33086)
PR_EMS_AB_SUPPORTING_STACK_O = PROP_TAG(PT_OBJECT, 33086)
PR_EMS_AB_SUPPORTING_STACK_T = PROP_TAG(PT_MV_TSTRING, 33086)
PR_EMS_AB_SUPPORTING_STACK_BL = PROP_TAG(PT_MV_TSTRING, 33087)
PR_EMS_AB_SUPPORTING_STACK_BL_A = PROP_TAG(PT_MV_STRING8, 33087)
PR_EMS_AB_SUPPORTING_STACK_BL_W = PROP_TAG(PT_MV_UNICODE, 33087)
PR_EMS_AB_SUPPORTING_STACK_BL_O = PROP_TAG(PT_OBJECT, 33087)
PR_EMS_AB_SUPPORTING_STACK_BL_T = PROP_TAG(PT_MV_TSTRING, 33087)
PR_EMS_AB_T_SELECTOR = PROP_TAG(PT_BINARY, 33088)
PR_EMS_AB_T_SELECTOR_INBOUND = PROP_TAG(PT_BINARY, 33089)
PR_EMS_AB_TARGET_ADDRESS = PROP_TAG(PT_TSTRING, 32785)
PR_EMS_AB_TARGET_ADDRESS_A = PROP_TAG(PT_STRING8, 32785)
PR_EMS_AB_TARGET_ADDRESS_W = PROP_TAG(PT_UNICODE, 32785)
PR_EMS_AB_TARGET_MTAS = PROP_TAG(PT_MV_TSTRING, 33090)
PR_EMS_AB_TARGET_MTAS_A = PROP_TAG(PT_MV_STRING8, 33090)
PR_EMS_AB_TARGET_MTAS_W = PROP_TAG(PT_MV_UNICODE, 33090)
PR_EMS_AB_TELEPHONE_NUMBER = PROP_TAG(PT_MV_TSTRING, 32786)
PR_EMS_AB_TELEPHONE_NUMBER_A = PROP_TAG(PT_MV_STRING8, 32786)
PR_EMS_AB_TELEPHONE_NUMBER_W = PROP_TAG(PT_MV_UNICODE, 32786)
PR_EMS_AB_TELETEX_TERMINAL_IDENTIFIER = PROP_TAG(PT_MV_BINARY, 33091)
PR_EMS_AB_TEMP_ASSOC_THRESHOLD = PROP_TAG(PT_LONG, 33092)
PR_EMS_AB_TOMBSTONE_LIFETIME = PROP_TAG(PT_LONG, 33093)
PR_EMS_AB_TRACKING_LOG_PATH_NAME = PROP_TAG(PT_TSTRING, 33094)
PR_EMS_AB_TRACKING_LOG_PATH_NAME_A = PROP_TAG(PT_STRING8, 33094)
PR_EMS_AB_TRACKING_LOG_PATH_NAME_W = PROP_TAG(PT_UNICODE, 33094)
PR_EMS_AB_TRANS_RETRY_MINS = PROP_TAG(PT_LONG, 33095)
PR_EMS_AB_TRANS_TIMEOUT_MINS = PROP_TAG(PT_LONG, 33096)
PR_EMS_AB_TRANSFER_RETRY_INTERVAL = PROP_TAG(PT_LONG, 33097)
PR_EMS_AB_TRANSFER_TIMEOUT_NON_URGENT = PROP_TAG(PT_LONG, 33098)
PR_EMS_AB_TRANSFER_TIMEOUT_NORMAL = PROP_TAG(PT_LONG, 33099)
PR_EMS_AB_TRANSFER_TIMEOUT_URGENT = PROP_TAG(PT_LONG, 33100)
PR_EMS_AB_TRANSLATION_TABLE_USED = PROP_TAG(PT_LONG, 33101)
PR_EMS_AB_TRANSPORT_EXPEDITED_DATA = PROP_TAG(PT_BOOLEAN, 33102)
PR_EMS_AB_TRUST_LEVEL = PROP_TAG(PT_LONG, 33103)
PR_EMS_AB_TURN_REQUEST_THRESHOLD = PROP_TAG(PT_LONG, 33104)
PR_EMS_AB_TWO_WAY_ALTERNATE_FACILITY = PROP_TAG(PT_BOOLEAN, 33105)
PR_EMS_AB_UNAUTH_ORIG_BL = PROP_TAG(PT_MV_TSTRING, 33106)
PR_EMS_AB_UNAUTH_ORIG_BL_A = PROP_TAG(PT_MV_STRING8, 33106)
PR_EMS_AB_UNAUTH_ORIG_BL_W = PROP_TAG(PT_MV_UNICODE, 33106)
PR_EMS_AB_UNAUTH_ORIG_BL_O = PROP_TAG(PT_OBJECT, 33106)
PR_EMS_AB_UNAUTH_ORIG_BL_T = PROP_TAG(PT_MV_TSTRING, 33106)
PR_EMS_AB_USE_SERVER_VALUES = PROP_TAG(PT_BOOLEAN, 33150)
PR_EMS_AB_USER_PASSWORD = PROP_TAG(PT_MV_BINARY, 33107)
PR_EMS_AB_USN_CHANGED = PROP_TAG(PT_LONG, 32809)
PR_EMS_AB_USN_CREATED = PROP_TAG(PT_LONG, 33108)
PR_EMS_AB_USN_DSA_LAST_OBJ_REMOVED = PROP_TAG(PT_LONG, 33109)
PR_EMS_AB_USN_INTERSITE = PROP_TAG(PT_LONG, 33146)
PR_EMS_AB_USN_LAST_OBJ_REM = PROP_TAG(PT_LONG, 33110)
PR_EMS_AB_USN_SOURCE = PROP_TAG(PT_LONG, 33111)
PR_EMS_AB_WWW_HOME_PAGE = PROP_TAG(PT_TSTRING, 33141)
PR_EMS_AB_WWW_HOME_PAGE_A = PROP_TAG(PT_STRING8, 33141)
PR_EMS_AB_WWW_HOME_PAGE_W = PROP_TAG(PT_UNICODE, 33141)
PR_EMS_AB_X121_ADDRESS = PROP_TAG(PT_MV_TSTRING, 33112)
PR_EMS_AB_X121_ADDRESS_A = PROP_TAG(PT_MV_STRING8, 33112)
PR_EMS_AB_X121_ADDRESS_W = PROP_TAG(PT_MV_UNICODE, 33112)
PR_EMS_AB_X25_CALL_USER_DATA_INCOMING = PROP_TAG(PT_BINARY, 33113)
PR_EMS_AB_X25_CALL_USER_DATA_OUTGOING = PROP_TAG(PT_BINARY, 33114)
PR_EMS_AB_X25_FACILITIES_DATA_INCOMING = PROP_TAG(PT_BINARY, 33115)
PR_EMS_AB_X25_FACILITIES_DATA_OUTGOING = PROP_TAG(PT_BINARY, 33116)
PR_EMS_AB_X25_LEASED_LINE_PORT = PROP_TAG(PT_BINARY, 33117)
PR_EMS_AB_X25_LEASED_OR_SWITCHED = PROP_TAG(PT_BOOLEAN, 33118)
PR_EMS_AB_X25_REMOTE_MTA_PHONE = PROP_TAG(PT_TSTRING, 33119)
PR_EMS_AB_X25_REMOTE_MTA_PHONE_A = PROP_TAG(PT_STRING8, 33119)
PR_EMS_AB_X25_REMOTE_MTA_PHONE_W = PROP_TAG(PT_UNICODE, 33119)
PR_EMS_AB_X400_ATTACHMENT_TYPE = PROP_TAG(PT_BINARY, 33120)
PR_EMS_AB_X400_SELECTOR_SYNTAX = PROP_TAG(PT_LONG, 33121)
PR_EMS_AB_X500_ACCESS_CONTROL_LIST = PROP_TAG(PT_BINARY, 33122)
PR_EMS_AB_XMIT_TIMEOUT_NON_URGENT = PROP_TAG(PT_LONG, 33123)
PR_EMS_AB_XMIT_TIMEOUT_NORMAL = PROP_TAG(PT_LONG, 33124)
PR_EMS_AB_XMIT_TIMEOUT_URGENT = PROP_TAG(PT_LONG, 33125)
