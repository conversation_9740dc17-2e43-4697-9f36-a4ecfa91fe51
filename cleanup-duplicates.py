#!/usr/bin/env python3
"""
清理重复文件和整理mcp目录
"""

import os
import shutil
from pathlib import Path

def cleanup_mcp_directory():
    """清理mcp目录中的重复文件"""
    print("🧹 清理mcp目录中的重复文件...")
    
    # 移动mcp目录中的文档到docs目录
    mcp_docs = [
        'mcp/DEPLOYMENT_SUCCESS_REPORT.md',
        'mcp/MCP_DEPLOYMENT_GUIDE.md'
    ]
    
    for doc in mcp_docs:
        source = Path(doc)
        if source.exists():
            target = Path('docs/mcp') / source.name
            try:
                shutil.move(str(source), str(target))
                print(f"📁 移动: {doc} -> docs/mcp/")
            except Exception as e:
                print(f"❌ 移动失败 {doc}: {e}")
    
    # 移动mcp目录中的脚本到scripts目录
    mcp_scripts = [
        'mcp/start_browser_tools.bat',
        'mcp/start_filesystem.bat',
        'mcp/test_mcp_installation.py'
    ]
    
    for script in mcp_scripts:
        source = Path(script)
        if source.exists():
            if script.endswith('.bat'):
                target = Path('scripts/setup') / source.name
            else:
                target = Path('tools/testing') / source.name
            try:
                shutil.move(str(source), str(target))
                print(f"📁 移动: {script} -> {target.parent}/")
            except Exception as e:
                print(f"❌ 移动失败 {script}: {e}")

def create_final_readme():
    """创建最终的README文件"""
    readme_content = """# 🎉 Claudia AI Assistant with MCP Servers

一个功能强大的AI助手，集成了9个MCP (Model Context Protocol) 服务器，提供文件操作、搜索、内存管理、浏览器自动化等功能。

## 🚀 快速开始

### 1. 启动应用
```bash
# 运行主启动器
launch.bat

# 或直接启动Claudia
scripts/setup/start-claudia.bat
```

### 2. 配置MCP服务器
```bash
# 使用导入助手
tools/import/import-helper-en.bat

# 或手动配置，参考文档
docs/mcp/FINAL_MCP_CONFIGURATION.md
```

### 3. 测试功能
```bash
# 测试所有MCP服务器
python tools/testing/test-all-mcp-servers.py
```

## 📦 MCP服务器功能

| 服务器 | 功能 | 状态 |
|--------|------|------|
| 📁 **Filesystem** | 文件系统操作 | ✅ 就绪 |
| 🧠 **Memory** | 内存管理 | ✅ 就绪 |
| 🔄 **Sequential Thinking** | 顺序思维 | ✅ 就绪 |
| 🔍 **Everything** | 文件搜索 | ✅ 就绪 |
| 🌐 **Fetch** | HTTP请求 | ✅ 就绪 |
| 📦 **Git** | Git操作 | ✅ 就绪 |
| ⏰ **Time** | 时间工具 | ✅ 就绪 |
| 🎯 **Context7** | 代码上下文 | ✅ 就绪 |
| 🤖 **Puppeteer** | 浏览器自动化 | ✅ 就绪 |

## 📁 项目结构

```
claudia/
├── 📖 docs/              # 文档
│   ├── mcp/              # MCP相关文档
│   └── setup/            # 设置文档
├── ⚙️ configs/           # 配置文件
│   └── mcp/              # MCP配置
├── 🔧 scripts/           # 脚本
│   ├── setup/            # 启动脚本
│   └── mcp/              # MCP脚本
├── 🛠️ tools/             # 工具
│   ├── import/           # 导入工具
│   └── testing/          # 测试工具
├── 📦 mcp/               # MCP服务器源码
├── 💻 src/               # 应用源码
├── 🏗️ src-tauri/         # Tauri后端
└── 🚀 launch.bat         # 主启动器
```

## 📚 重要文档

- **[项目结构说明](PROJECT_STRUCTURE.md)** - 详细的目录结构
- **[MCP配置指南](docs/mcp/FINAL_MCP_CONFIGURATION.md)** - 完整配置指南
- **[导入步骤](docs/mcp/STEP_BY_STEP_IMPORT.md)** - 详细导入步骤
- **[功能总结](docs/mcp/IMPORT_READY_SUMMARY.md)** - 功能概览

## 🔧 开发

### 启动开发环境
```bash
npm run tauri dev
```

### 构建应用
```bash
npm run tauri build
```

### 测试MCP服务器
```bash
python tools/testing/test-all-mcp-servers.py
```

## 🆘 故障排除

1. **MCP服务器无法连接**
   - 检查依赖是否安装: `python tools/testing/verify-mcp-setup.py`
   - 重新配置服务器: `scripts/setup/configure-mcp-servers.bat`

2. **应用无法启动**
   - 检查Node.js和Rust环境
   - 重新安装依赖: `npm install`

3. **导入失败**
   - 使用手动导入: 参考 `docs/mcp/STEP_BY_STEP_IMPORT.md`
   - 检查配置文件: `configs/mcp/`

## 📄 许可证

本项目基于 MIT 许可证开源。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

**享受强大的AI助手体验！** 🚀
"""
    
    with open('README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("✅ 创建新的README.md")

def main():
    """主函数"""
    print("🧹 开始最终清理...")
    print("=" * 50)
    
    # 清理mcp目录
    cleanup_mcp_directory()
    
    # 创建最终README
    create_final_readme()
    
    print("\n" + "=" * 50)
    print("🎉 项目整理完成！")
    print("\n📋 整理后的项目结构:")
    print("✅ 所有文档已整理到 docs/ 目录")
    print("✅ 所有配置已整理到 configs/ 目录") 
    print("✅ 所有脚本已整理到 scripts/ 目录")
    print("✅ 所有工具已整理到 tools/ 目录")
    print("✅ 创建了主启动器 launch.bat")
    print("✅ 更新了项目README.md")
    print("\n🚀 现在可以运行 launch.bat 开始使用！")

if __name__ == "__main__":
    main()
