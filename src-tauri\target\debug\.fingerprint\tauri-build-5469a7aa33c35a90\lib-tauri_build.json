{"rustc": 1842507548689473721, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 12844171384082609526, "deps": [[4899080583175475170, "semver", false, 6587108540636782383], [6913375703034175521, "schemars", false, 4311322490016638138], [7170110829644101142, "json_patch", false, 15473634152578215941], [9689903380558560274, "serde", false, 8619023586440547223], [11050281405049894993, "tauri_utils", false, 8557585670532656720], [12714016054753183456, "tauri_winres", false, 1614107797804706055], [13077543566650298139, "heck", false, 12227043807642275620], [13475171727366188400, "cargo_toml", false, 16168951604840718634], [13625485746686963219, "anyhow", false, 16521415306001099308], [15367738274754116744, "serde_json", false, 4475620309241193984], [15609422047640926750, "toml", false, 1657660958671004585], [15622660310229662834, "walkdir", false, 4450029676203649764], [16928111194414003569, "dirs", false, 18065998891642873765], [17155886227862585100, "glob", false, 16666287207728800018]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-5469a7aa33c35a90\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}