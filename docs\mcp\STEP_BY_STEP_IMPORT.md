# 🎯 MCP服务器导入详细步骤

## 📋 准备工作 ✅ 已完成

- ✅ 9个MCP服务器已准备就绪
- ✅ 配置文件已创建: `mcp_import_config.json`
- ✅ Claudia应用正在运行: http://localhost:1420

## 🚀 导入步骤

### 方法1: 使用配置文件导入 (推荐)

1. **打开Claudia**
   - 访问 http://localhost:1420
   - 确保页面完全加载

2. **找到MCP选项**
   - 在页面底部的对话框区域
   - 查找 **MCP** 按钮或选项
   - 点击打开MCP面板

3. **进入导入界面**
   - 在MCP面板中找到 **Import/Export** 标签页
   - 点击切换到导入界面

4. **导入配置文件**
   - 点击 **Choose File** 或 **Browse** 按钮
   - 选择文件: `C:\Users\<USER>\Desktop\claudia\mcp_import_config.json`
   - 点击 **Import** 按钮

5. **验证导入**
   - 检查是否显示导入成功消息
   - 切换到 **Servers** 标签页查看服务器列表
   - 确认所有9个服务器都已添加

### 方法2: 使用浏览器控制台脚本

如果配置文件导入不工作，可以使用控制台脚本：

1. **打开开发者工具**
   - 在Claudia页面按 `F12` 或 `Ctrl+Shift+I`
   - 切换到 **Console** 标签页

2. **运行导入脚本**
   ```javascript
   // 复制并粘贴以下代码到控制台，然后按回车
   
   const mcpServers = [
     {
       name: "filesystem",
       command: "node",
       args: ["C:\\Users\\<USER>\\Desktop\\claudia\\mcp\\servers\\src\\filesystem\\dist\\index.js"]
     },
     {
       name: "memory",
       command: "node",
       args: ["C:\\Users\\<USER>\\Desktop\\claudia\\mcp\\servers\\src\\memory\\dist\\index.js"]
     },
     {
       name: "sequentialthinking",
       command: "node",
       args: ["C:\\Users\\<USER>\\Desktop\\claudia\\mcp\\servers\\src\\sequentialthinking\\dist\\index.js"]
     },
     {
       name: "everything",
       command: "node",
       args: ["C:\\Users\\<USER>\\Desktop\\claudia\\mcp\\servers\\src\\everything\\dist\\index.js"]
     },
     {
       name: "fetch",
       command: "python",
       args: ["-m", "mcp_server_fetch"]
     },
     {
       name: "git",
       command: "python",
       args: ["-m", "mcp_server_git"]
     },
     {
       name: "time",
       command: "python",
       args: ["-m", "mcp_server_time"]
     },
     {
       name: "context7",
       command: "node",
       args: ["C:\\Users\\<USER>\\Desktop\\claudia\\node_modules\\@upstash\\context7-mcp\\dist\\index.js"]
     },
     {
       name: "puppeteer",
       command: "node",
       args: ["C:\\Users\\<USER>\\Desktop\\claudia\\node_modules\\puppeteer-mcp-server\\dist\\index.js"]
     }
   ];
   
   async function importServers() {
     if (!window.__TAURI__) {
       console.error('Tauri API不可用');
       return;
     }
     
     const { invoke } = window.__TAURI__.tauri;
     let success = 0;
     
     for (const server of mcpServers) {
       try {
         const result = await invoke('mcp_add', {
           name: server.name,
           transport: 'stdio',
           command: server.command,
           args: server.args,
           env: {},
           scope: 'project'
         });
         
         if (result.success) {
           console.log(`✅ ${server.name} 添加成功`);
           success++;
         } else {
           console.error(`❌ ${server.name} 失败: ${result.message}`);
         }
       } catch (error) {
         console.error(`❌ ${server.name} 异常:`, error);
       }
       
       await new Promise(r => setTimeout(r, 1000));
     }
     
     console.log(`🎉 导入完成: ${success}/${mcpServers.length} 成功`);
   }
   
   importServers();
   ```

### 方法3: 手动逐个添加

如果以上方法都不工作，可以手动添加每个服务器：

1. **打开MCP设置**
   - 在MCP面板中选择 **Add Server** 标签页

2. **添加每个服务器**
   
   **Filesystem:**
   ```
   Name: filesystem
   Transport: stdio
   Command: node
   Args: C:\Users\<USER>\Desktop\claudia\mcp\servers\src\filesystem\dist\index.js
   Scope: project
   ```
   
   **Memory:**
   ```
   Name: memory
   Transport: stdio
   Command: node
   Args: C:\Users\<USER>\Desktop\claudia\mcp\servers\src\memory\dist\index.js
   Scope: project
   ```
   
   **Sequential Thinking:**
   ```
   Name: sequentialthinking
   Transport: stdio
   Command: node
   Args: C:\Users\<USER>\Desktop\claudia\mcp\servers\src\sequentialthinking\dist\index.js
   Scope: project
   ```
   
   **Everything:**
   ```
   Name: everything
   Transport: stdio
   Command: node
   Args: C:\Users\<USER>\Desktop\claudia\mcp\servers\src\everything\dist\index.js
   Scope: project
   ```
   
   **Fetch:**
   ```
   Name: fetch
   Transport: stdio
   Command: python
   Args: -m mcp_server_fetch
   Scope: project
   ```
   
   **Git:**
   ```
   Name: git
   Transport: stdio
   Command: python
   Args: -m mcp_server_git
   Scope: project
   ```
   
   **Time:**
   ```
   Name: time
   Transport: stdio
   Command: python
   Args: -m mcp_server_time
   Scope: project
   ```
   
   **Context7:**
   ```
   Name: context7
   Transport: stdio
   Command: node
   Args: C:\Users\<USER>\Desktop\claudia\node_modules\@upstash\context7-mcp\dist\index.js
   Scope: project
   ```
   
   **Puppeteer:**
   ```
   Name: puppeteer
   Transport: stdio
   Command: node
   Args: C:\Users\<USER>\Desktop\claudia\node_modules\puppeteer-mcp-server\dist\index.js
   Scope: project
   ```

## 🧪 验证导入成功

导入完成后，请检查：

1. **服务器列表**
   - 在MCP面板的 **Servers** 标签页
   - 应该看到所有9个服务器

2. **连接状态**
   - 每个服务器应显示为 "Connected" 或 "Running"
   - 如果显示错误，点击 "Test Connection" 重试

3. **功能测试**
   - 在Claude对话中尝试使用MCP功能
   - 例如：请求文件操作、搜索等

## 🎉 完成确认

当您看到：
- ✅ 9个服务器全部显示在列表中
- ✅ 连接状态正常
- ✅ 可以在对话中使用MCP功能

说明导入成功！您现在拥有了功能完整的Claudia AI助手！

## 🆘 需要帮助？

如果遇到问题：
1. 检查Claudia控制台是否有错误信息
2. 确认所有依赖都已正确安装
3. 重启Claudia应用后重试
