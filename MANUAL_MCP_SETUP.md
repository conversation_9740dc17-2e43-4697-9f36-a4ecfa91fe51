# 🔧 Claudia MCP服务器手动配置指南

## 🎯 配置目标

在Claudia中配置以下7个MCP服务器：
- 📁 **filesystem** - 文件系统操作
- 🔍 **everything** - 快速文件搜索
- 🧠 **memory** - 内存管理
- 🔄 **sequentialthinking** - 顺序思维
- 🌐 **fetch** - HTTP请求
- 📦 **git** - Git操作
- ⏰ **time** - 时间工具

## 📋 前置条件

✅ **已完成**:
- [x] MCP服务器已复制到项目目录
- [x] Node.js依赖已安装
- [x] Python依赖已安装
- [x] TypeScript项目已编译
- [x] 所有服务器测试通过

## 🚀 配置步骤

### 步骤1: 打开Claudia MCP设置

1. 确保Claudia正在运行 (http://localhost:1420)
2. 在对话框底部找到 **MCP** 选项
3. 点击展开MCP面板
4. 选择 **Settings** 或 **Add Server** 选项

### 步骤2: 添加Node.js服务器

#### 📁 Filesystem Server
```
Name: filesystem
Transport: stdio
Command: node
Args: mcp/servers/src/filesystem/dist/index.js
Env: (留空)
Scope: project
```

#### 🔍 Everything Server
```
Name: everything
Transport: stdio
Command: node
Args: mcp/servers/src/everything/dist/index.js
Env: (留空)
Scope: project
```

#### 🧠 Memory Server
```
Name: memory
Transport: stdio
Command: node
Args: mcp/servers/src/memory/dist/index.js
Env: (留空)
Scope: project
```

#### 🔄 Sequential Thinking Server
```
Name: sequentialthinking
Transport: stdio
Command: node
Args: mcp/servers/src/sequentialthinking/dist/index.js
Env: (留空)
Scope: project
```

### 步骤3: 添加Python服务器

#### 🌐 Fetch Server
```
Name: fetch
Transport: stdio
Command: python
Args: -m mcp_server_fetch
Env: (留空)
Scope: project
```

#### 📦 Git Server
```
Name: git
Transport: stdio
Command: python
Args: -m mcp_server_git
Env: (留空)
Scope: project
```

#### ⏰ Time Server
```
Name: time
Transport: stdio
Command: python
Args: -m mcp_server_time
Env: (留空)
Scope: project
```

## 📝 配置注意事项

### Args字段格式
- **单个参数**: 直接输入，如 `mcp/servers/src/filesystem/dist/index.js`
- **多个参数**: 用空格分隔，如 `-m mcp_server_fetch`

### 路径说明
- 所有路径都是相对于项目根目录 (`C:\Users\<USER>\Desktop\claudia`)
- Node.js服务器使用编译后的 `.js` 文件
- Python服务器使用模块导入方式

### Scope选择
- **project**: 仅在当前项目中可用
- **local**: 在本地所有项目中可用
- **user**: 在用户级别全局可用

推荐使用 **project** scope。

## 🧪 验证配置

配置完成后：

1. **检查服务器状态**: 每个服务器应显示为 "Connected" 或 "Running"
2. **测试连接**: 点击每个服务器的 "Test Connection" 按钮
3. **查看日志**: 检查是否有错误信息

## 🔄 导入配置文件

如果Claudia支持配置文件导入：

1. 使用 `claude_desktop_config.json` 文件
2. 在MCP设置中选择 "Import" 或 "Import from Claude Desktop"
3. 选择配置文件并导入

## 🛠️ 故障排除

### 常见问题

#### 1. 服务器无法启动
- 检查命令路径是否正确
- 确认依赖已正确安装
- 查看错误日志

#### 2. Python模块找不到
```bash
# 重新安装Python服务器
cd mcp/servers/src/fetch && pip install -e .
cd ../git && pip install -e .
cd ../time && pip install -e .
```

#### 3. Node.js文件找不到
```bash
# 重新编译TypeScript
cd mcp/servers/src/filesystem && npm run build
cd ../everything && npm run build
cd ../memory && npm run build
cd ../sequentialthinking && npm run build
```

### 测试命令

验证服务器是否可以独立运行：

```bash
# 测试Node.js服务器
node mcp/servers/src/filesystem/dist/index.js

# 测试Python服务器
python -m mcp_server_fetch
```

## 🎉 完成确认

配置完成后，你应该看到：
- ✅ 7个MCP服务器全部显示为已连接
- ✅ 在Claude对话中可以使用MCP功能
- ✅ 没有错误或警告信息

## 📞 获取帮助

如果遇到问题：
1. 查看 `MCP_SERVERS_CONFIG.md` 获取详细信息
2. 运行 `python test-mcp-servers.py` 验证服务器状态
3. 检查Claudia的控制台日志
