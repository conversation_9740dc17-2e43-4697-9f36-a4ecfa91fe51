{"mcpServers": {"filesystem": {"command": "node", "args": ["mcp/servers/src/filesystem/dist/index.js"]}, "everything": {"command": "node", "args": ["mcp/servers/src/everything/dist/index.js"]}, "memory": {"command": "node", "args": ["mcp/servers/src/memory/dist/index.js"]}, "sequentialthinking": {"command": "node", "args": ["mcp/servers/src/sequentialthinking/dist/index.js"]}, "fetch": {"command": "python", "args": ["-m", "mcp_server_fetch"]}, "git": {"command": "python", "args": ["-m", "mcp_server_git"]}, "time": {"command": "python", "args": ["-m", "mcp_server_time"]}}}