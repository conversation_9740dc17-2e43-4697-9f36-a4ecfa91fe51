#!/usr/bin/env node
/**
 * 直接通过HTTP API导入MCP服务器到Claudia
 * 使用Claudia的内部API接口
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// MCP服务器配置
const mcpServers = [
  {
    name: "filesystem",
    command: "node",
    args: ["C:\\Users\\<USER>\\Desktop\\claudia\\mcp\\servers\\src\\filesystem\\dist\\index.js"],
    env: {}
  },
  {
    name: "memory",
    command: "node", 
    args: ["C:\\Users\\<USER>\\Desktop\\claudia\\mcp\\servers\\src\\memory\\dist\\index.js"],
    env: {}
  },
  {
    name: "sequentialthinking",
    command: "node",
    args: ["C:\\Users\\<USER>\\Desktop\\claudia\\mcp\\servers\\src\\sequentialthinking\\dist\\index.js"],
    env: {}
  },
  {
    name: "everything",
    command: "node",
    args: ["C:\\Users\\<USER>\\Desktop\\claudia\\mcp\\servers\\src\\everything\\dist\\index.js"],
    env: {}
  },
  {
    name: "fetch",
    command: "python",
    args: ["-m", "mcp_server_fetch"],
    env: {}
  },
  {
    name: "git",
    command: "python",
    args: ["-m", "mcp_server_git"],
    env: {}
  },
  {
    name: "time",
    command: "python",
    args: ["-m", "mcp_server_time"],
    env: {}
  },
  {
    name: "context7",
    command: "node",
    args: ["C:\\Users\\<USER>\\Desktop\\claudia\\node_modules\\@upstash\\context7-mcp\\dist\\index.js"],
    env: {}
  },
  {
    name: "puppeteer",
    command: "node",
    args: ["C:\\Users\\<USER>\\Desktop\\claudia\\node_modules\\puppeteer-mcp-server\\dist\\index.js"],
    env: {}
  }
];

async function checkClaudiaRunning() {
  try {
    const response = await fetch('http://localhost:1420');
    return response.ok;
  } catch (error) {
    return false;
  }
}

async function addMCPServer(server) {
  console.log(`🔧 添加MCP服务器: ${server.name}`);
  
  // 创建JSON配置
  const serverConfig = {
    type: "stdio",
    command: server.command,
    args: server.args,
    env: server.env
  };
  
  const jsonConfig = JSON.stringify(serverConfig);
  
  // 尝试通过不同的方式调用API
  const methods = [
    // 方法1: 直接调用Tauri API (如果可用)
    async () => {
      if (typeof window !== 'undefined' && window.__TAURI__) {
        const { invoke } = window.__TAURI__.tauri;
        return await invoke('mcp_add_json', {
          name: server.name,
          jsonConfig: jsonConfig,
          scope: 'project'
        });
      }
      throw new Error('Tauri API not available');
    },
    
    // 方法2: 通过HTTP请求 (如果有API端点)
    async () => {
      const response = await fetch('http://localhost:1420/api/mcp/add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: server.name,
          transport: 'stdio',
          command: server.command,
          args: server.args,
          env: server.env,
          scope: 'project'
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
    }
  ];
  
  for (const method of methods) {
    try {
      const result = await method();
      if (result && result.success) {
        console.log(`✅ ${server.name} 添加成功`);
        return true;
      } else {
        console.log(`❌ ${server.name} 添加失败: ${result?.message || '未知错误'}`);
        return false;
      }
    } catch (error) {
      // 继续尝试下一个方法
      continue;
    }
  }
  
  console.log(`❌ ${server.name} 所有方法都失败了`);
  return false;
}

async function createConfigFile() {
  // 创建配置文件供手动导入
  const config = {
    mcpServers: {}
  };
  
  mcpServers.forEach(server => {
    config.mcpServers[server.name] = {
      command: server.command,
      args: server.args,
      env: server.env
    };
  });
  
  const configPath = path.join(__dirname, 'mcp_import_config.json');
  fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
  console.log(`✅ 配置文件已创建: ${configPath}`);
  
  return configPath;
}

async function main() {
  console.log('🚀 开始导入MCP服务器到Claudia...');
  console.log('=' .repeat(60));
  
  // 检查Claudia是否运行
  const isRunning = await checkClaudiaRunning();
  if (!isRunning) {
    console.log('❌ Claudia应用未运行，请先启动Claudia');
    console.log('   运行命令: npm run tauri dev');
    process.exit(1);
  }
  
  console.log('✅ Claudia应用正在运行');
  console.log(`📋 准备导入 ${mcpServers.length} 个MCP服务器`);
  console.log();
  
  // 创建配置文件
  const configPath = await createConfigFile();
  
  // 尝试自动导入
  let successCount = 0;
  
  for (const server of mcpServers) {
    const success = await addMCPServer(server);
    if (success) {
      successCount++;
    }
    
    // 添加延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log();
  console.log('=' .repeat(60));
  console.log(`📊 导入结果: ${successCount}/${mcpServers.length} 成功`);
  
  if (successCount === mcpServers.length) {
    console.log('🎉 所有MCP服务器导入成功！');
  } else if (successCount > 0) {
    console.log('⚠️ 部分服务器导入成功');
  } else {
    console.log('❌ 自动导入失败，请手动导入');
  }
  
  console.log();
  console.log('📖 手动导入步骤:');
  console.log('1. 打开 http://localhost:1420');
  console.log('2. 在对话框底部点击 MCP 选项');
  console.log('3. 选择 Import/Export 标签页');
  console.log(`4. 导入配置文件: ${configPath}`);
  console.log();
  console.log('🌐 正在打开Claudia...');
  
  // 打开浏览器
  const { exec } = await import('child_process');
  exec('start http://localhost:1420', (error) => {
    if (error) {
      console.log('请手动打开: http://localhost:1420');
    }
  });
}

main().catch(error => {
  console.error('❌ 导入失败:', error.message);
  process.exit(1);
});
