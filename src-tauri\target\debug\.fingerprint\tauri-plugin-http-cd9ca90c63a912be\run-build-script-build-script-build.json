{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 6797351182006103954], [13890802266741835355, "build_script_build", false, 678354854607061739], [15441187897486245138, "build_script_build", false, 6375631606775283593]], "local": [{"RerunIfChanged": {"output": "debug\\build\\tauri-plugin-http-cd9ca90c63a912be\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}