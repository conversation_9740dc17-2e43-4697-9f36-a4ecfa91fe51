{"rustc": 1842507548689473721, "features": "[\"bundled\", \"modern_sqlite\"]", "declared_features": "[\"array\", \"backup\", \"blob\", \"buildtime_bindgen\", \"bundled\", \"bundled-full\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"chrono\", \"collation\", \"column_decltype\", \"csv\", \"csvtab\", \"extra_check\", \"functions\", \"hooks\", \"i128_blob\", \"in_gecko\", \"limits\", \"load_extension\", \"loadable_extension\", \"modern-full\", \"modern_sqlite\", \"preupdate_hook\", \"release_memory\", \"rusqlite-macros\", \"serde_json\", \"serialize\", \"series\", \"session\", \"sqlcipher\", \"time\", \"trace\", \"unlock_notify\", \"url\", \"uuid\", \"vtab\", \"wasm32-wasi-vfs\", \"window\", \"with-asan\"]", "target": 10662205063260755052, "profile": 15657897354478470176, "path": 13718170166304641978, "deps": [[3056352129074654578, "hashlink", false, 6387880004864817246], [3666196340704888985, "smallvec", false, 8310769828680938795], [5510864063823219921, "fallible_streaming_iterator", false, 16400729130438605637], [7896293946984509699, "bitflags", false, 9400886434190491140], [12860549049674006569, "fallible_iterator", false, 14209067283297072839], [16675652872862304210, "libsqlite3_sys", false, 11459683257898493600]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rusqlite-9820ac9b254a66bf\\dep-lib-rusqlite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}