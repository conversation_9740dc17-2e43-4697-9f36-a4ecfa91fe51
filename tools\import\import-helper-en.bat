@echo off
title MCP Server Import Helper
echo.
echo ========================================
echo    MCP Server Import Helper
echo ========================================
echo.
echo Ready to import 9 MCP servers:
echo - filesystem, memory, sequentialthinking, everything
echo - fetch, git, time, context7, puppeteer
echo.
echo Opening Claudia and config file location...
echo.

REM Open Claudia page
start http://localhost:1420

REM Open config file directory
start explorer "%~dp0"

echo.
echo ========================================
echo    Import Instructions
echo ========================================
echo.
echo Method 1: File Import (Recommended)
echo.
echo 1. In Claudia page, find MCP option at bottom
echo 2. Click to open MCP panel
echo 3. Select Import/Export tab
echo 4. Click Choose File button
echo 5. Select: mcp_import_config.json
echo 6. Click Import button
echo.
echo ========================================
echo.
echo Method 2: Browser Console Script
echo.
echo 1. Press F12 in Claudia page
echo 2. Go to Console tab
echo 3. Copy and paste the script from STEP_BY_STEP_IMPORT.md
echo 4. Press Enter to run
echo.
echo ========================================
echo.
echo Verification:
echo - Check MCP panel for 9 servers
echo - Ensure all show as Connected
echo - Test MCP functions in chat
echo.
echo Files ready:
echo - Config: mcp_import_config.json
echo - Guide: STEP_BY_STEP_IMPORT.md
echo.
pause
