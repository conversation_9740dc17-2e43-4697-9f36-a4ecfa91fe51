@echo off
title MCP服务器导入助手
color 0A
echo.
echo ========================================
echo    🎯 MCP服务器导入助手
echo ========================================
echo.
echo ✅ 准备工作已完成:
echo    📦 9个MCP服务器已准备就绪
echo    📁 配置文件: mcp_import_config.json
echo    🌐 Claudia运行中: http://localhost:1420
echo.
echo 🚀 开始导入过程...
echo.

REM 打开Claudia页面
echo 🌐 正在打开Claudia...
start http://localhost:1420
timeout /t 3 /nobreak >nul

REM 打开配置文件所在目录
echo 📁 正在打开配置文件目录...
start explorer "%~dp0"
timeout /t 2 /nobreak >nul

echo.
echo ========================================
echo    📋 导入步骤指南
echo ========================================
echo.
echo 🎯 方法1: 配置文件导入 (推荐)
echo.
echo 1. 在Claudia页面底部找到 MCP 选项
echo 2. 点击打开MCP面板
echo 3. 选择 Import/Export 标签页
echo 4. 点击 Choose File 选择配置文件
echo 5. 选择: mcp_import_config.json
echo 6. 点击 Import 按钮
echo.
echo ========================================
echo.
echo 🎯 方法2: 浏览器控制台脚本
echo.
echo 1. 在Claudia页面按 F12 打开开发者工具
echo 2. 切换到 Console 标签页
echo 3. 复制粘贴以下代码并按回车:
echo.
echo const servers = [
echo   {name: "filesystem", command: "node", args: ["C:\\Users\\<USER>\\Desktop\\claudia\\mcp\\servers\\src\\filesystem\\dist\\index.js"]},
echo   {name: "memory", command: "node", args: ["C:\\Users\\<USER>\\Desktop\\claudia\\mcp\\servers\\src\\memory\\dist\\index.js"]},
echo   {name: "sequentialthinking", command: "node", args: ["C:\\Users\\<USER>\\Desktop\\claudia\\mcp\\servers\\src\\sequentialthinking\\dist\\index.js"]},
echo   {name: "everything", command: "node", args: ["C:\\Users\\<USER>\\Desktop\\claudia\\mcp\\servers\\src\\everything\\dist\\index.js"]},
echo   {name: "fetch", command: "python", args: ["-m", "mcp_server_fetch"]},
echo   {name: "git", command: "python", args: ["-m", "mcp_server_git"]},
echo   {name: "time", command: "python", args: ["-m", "mcp_server_time"]},
echo   {name: "context7", command: "node", args: ["C:\\Users\\<USER>\\Desktop\\claudia\\node_modules\\@upstash\\context7-mcp\\dist\\index.js"]},
echo   {name: "puppeteer", command: "node", args: ["C:\\Users\\<USER>\\Desktop\\claudia\\node_modules\\puppeteer-mcp-server\\dist\\index.js"]}
echo ];
echo.
echo async function importAll() {
echo   if (!window.__TAURI__) return console.error('Tauri API不可用');
echo   const {invoke} = window.__TAURI__.tauri;
echo   let success = 0;
echo   for (const s of servers) {
echo     try {
echo       const result = await invoke('mcp_add', {name: s.name, transport: 'stdio', command: s.command, args: s.args, env: {}, scope: 'project'});
echo       if (result.success) { console.log(`✅ ${s.name} 成功`); success++; }
echo       else console.error(`❌ ${s.name} 失败: ${result.message}`);
echo     } catch (e) { console.error(`❌ ${s.name} 异常:`, e); }
echo     await new Promise(r =^> setTimeout(r, 1000));
echo   }
echo   console.log(`🎉 完成: ${success}/${servers.length} 成功`);
echo }
echo importAll();
echo.
echo ========================================
echo.
echo 🧪 验证导入成功:
echo    1. 在MCP面板查看服务器列表
echo    2. 确认9个服务器都显示为Connected
echo    3. 在Claude对话中测试MCP功能
echo.
echo 📞 需要帮助? 查看: STEP_BY_STEP_IMPORT.md
echo.
echo ========================================
echo    🎉 准备完成！
echo ========================================
echo.
echo 💡 提示:
echo    - 配置文件已在文件管理器中打开
echo    - Claudia页面已在浏览器中打开
echo    - 按照上述步骤进行导入
echo.
pause
