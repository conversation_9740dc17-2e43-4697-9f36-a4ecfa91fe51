#!/usr/bin/env node
/**
 * 自动导入MCP服务器到Claudia
 * 通过Tauri API直接调用Claudia的MCP管理功能
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// MCP服务器配置
const mcpServers = {
  filesystem: {
    command: "node",
    args: [path.resolve(__dirname, "mcp/servers/src/filesystem/dist/index.js")],
    env: {}
  },
  memory: {
    command: "node",
    args: [path.resolve(__dirname, "mcp/servers/src/memory/dist/index.js")],
    env: {}
  },
  sequentialthinking: {
    command: "node",
    args: [path.resolve(__dirname, "mcp/servers/src/sequentialthinking/dist/index.js")],
    env: {}
  },
  everything: {
    command: "node",
    args: [path.resolve(__dirname, "mcp/servers/src/everything/dist/index.js")],
    env: {}
  },
  fetch: {
    command: "python",
    args: ["-m", "mcp_server_fetch"],
    env: {}
  },
  git: {
    command: "python",
    args: ["-m", "mcp_server_git"],
    env: {}
  },
  time: {
    command: "python",
    args: ["-m", "mcp_server_time"],
    env: {}
  },
  context7: {
    command: "node",
    args: [path.resolve(__dirname, "node_modules/@upstash/context7-mcp/dist/index.js")],
    env: {}
  },
  puppeteer: {
    command: "node",
    args: [path.resolve(__dirname, "node_modules/puppeteer-mcp-server/dist/index.js")],
    env: {}
  }
};

async function importToClaudia() {
  console.log('🚀 开始自动导入MCP服务器到Claudia...');
  console.log('=' .repeat(60));
  
  // 检查Claudia是否运行
  try {
    const response = await fetch('http://localhost:1420');
    if (!response.ok) {
      throw new Error('Claudia not responding');
    }
    console.log('✅ Claudia应用正在运行');
  } catch (error) {
    console.log('❌ Claudia应用未运行，请先启动Claudia');
    console.log('   运行命令: npm run tauri dev');
    process.exit(1);
  }
  
  // 创建Claude Desktop格式的配置文件
  const claudeDesktopConfig = {
    mcpServers: mcpServers
  };
  
  const configPath = path.join(__dirname, 'claude_desktop_import.json');
  fs.writeFileSync(configPath, JSON.stringify(claudeDesktopConfig, null, 2));
  console.log('✅ 已创建Claude Desktop格式配置文件');
  
  console.log('\n📋 准备导入的MCP服务器:');
  Object.keys(mcpServers).forEach((name, index) => {
    const server = mcpServers[name];
    console.log(`${index + 1}. 📦 ${name}`);
    console.log(`   命令: ${server.command}`);
    console.log(`   参数: ${server.args.join(' ')}`);
  });
  
  console.log('\n🔧 开始通过Claudia API导入服务器...');
  
  // 使用Claudia的导入功能
  try {
    // 方法1: 尝试使用Claude Desktop导入功能
    console.log('📥 尝试使用Claude Desktop导入功能...');
    
    // 这里我们需要通过浏览器自动化来触发导入
    await automateImport();
    
  } catch (error) {
    console.error('❌ 自动导入失败:', error.message);
    console.log('\n📖 请手动导入:');
    console.log('1. 打开 http://localhost:1420');
    console.log('2. 在对话框底部点击 MCP 选项');
    console.log('3. 选择 Import/Export 标签页');
    console.log('4. 导入 claude_desktop_import.json 文件');
  }
}

async function automateImport() {
  console.log('🤖 启动浏览器自动化导入...');
  
  // 动态导入puppeteer
  let puppeteer;
  try {
    puppeteer = await import('puppeteer');
  } catch (error) {
    throw new Error('Puppeteer未安装，无法自动导入。请手动导入配置文件。');
  }
  
  const browser = await puppeteer.default.launch({ 
    headless: false,
    defaultViewport: null,
    args: ['--start-maximized']
  });
  
  try {
    const page = await browser.newPage();
    
    console.log('🌐 打开Claudia页面...');
    await page.goto('http://localhost:1420', { waitUntil: 'networkidle0' });
    
    console.log('🔍 查找MCP选项...');
    // 等待页面加载完成
    await page.waitForTimeout(3000);
    
    // 查找MCP按钮或选项
    const mcpButton = await page.$('[data-testid="mcp-button"], button:has-text("MCP"), [aria-label*="MCP"]');
    
    if (mcpButton) {
      console.log('✅ 找到MCP选项，点击打开...');
      await mcpButton.click();
      await page.waitForTimeout(2000);
      
      // 查找Import/Export标签
      const importTab = await page.$('button:has-text("Import"), [data-testid="import-tab"]');
      if (importTab) {
        console.log('✅ 找到Import标签，点击...');
        await importTab.click();
        await page.waitForTimeout(1000);
        
        // 查找文件上传输入
        const fileInput = await page.$('input[type="file"]');
        if (fileInput) {
          console.log('✅ 找到文件上传输入，上传配置文件...');
          const configPath = path.resolve(__dirname, 'claude_desktop_import.json');
          await fileInput.uploadFile(configPath);
          await page.waitForTimeout(2000);
          
          // 查找导入按钮
          const importButton = await page.$('button:has-text("Import"), [data-testid="import-button"]');
          if (importButton) {
            console.log('✅ 点击导入按钮...');
            await importButton.click();
            await page.waitForTimeout(5000);
            
            console.log('🎉 自动导入完成！');
          } else {
            throw new Error('未找到导入按钮');
          }
        } else {
          throw new Error('未找到文件上传输入');
        }
      } else {
        throw new Error('未找到Import标签');
      }
    } else {
      throw new Error('未找到MCP选项');
    }
    
  } finally {
    // 保持浏览器打开以便用户查看结果
    console.log('🌐 浏览器将保持打开状态，请查看导入结果');
    console.log('   如需关闭浏览器，请手动关闭窗口');
    
    // 不自动关闭浏览器
    // await browser.close();
  }
}

async function main() {
  try {
    await importToClaudia();
  } catch (error) {
    console.error('❌ 导入失败:', error.message);
    process.exit(1);
  }
}

// 直接运行主函数
main();
