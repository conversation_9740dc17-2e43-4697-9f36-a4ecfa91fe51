#!/usr/bin/env python3
"""
MCP设置验证脚本
验证所有MCP服务器是否正确配置并可以运行
"""

import subprocess
import sys
import json
import time
import os
from pathlib import Path

def check_file_exists(file_path):
    """检查文件是否存在"""
    path = Path(file_path)
    if path.exists():
        print(f"✅ 文件存在: {file_path}")
        return True
    else:
        print(f"❌ 文件不存在: {file_path}")
        return False

def test_node_command(name, script_path):
    """测试Node.js命令是否可以执行"""
    print(f"🧪 测试 {name} (Node.js)...")
    
    if not check_file_exists(script_path):
        return False
    
    try:
        # 测试文件是否可以被Node.js加载
        result = subprocess.run(
            ["node", "-e", f"require('{script_path}')"],
            capture_output=True,
            text=True,
            timeout=5,
            cwd=Path.cwd()
        )
        
        if result.returncode == 0:
            print(f"✅ {name} Node.js脚本可以正常加载")
            return True
        else:
            print(f"❌ {name} Node.js脚本加载失败:")
            print(f"   错误: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⚠️ {name} 测试超时（可能正常，服务器在等待输入）")
        return True
    except Exception as e:
        print(f"❌ {name} 测试异常: {e}")
        return False

def test_python_module(name, module_name):
    """测试Python模块是否可以导入"""
    print(f"🧪 测试 {name} (Python)...")
    
    try:
        # 测试模块是否可以导入
        result = subprocess.run(
            ["python", "-c", f"import {module_name}; print('Module imported successfully')"],
            capture_output=True,
            text=True,
            timeout=5,
            cwd=Path.cwd()
        )
        
        if result.returncode == 0:
            print(f"✅ {name} Python模块可以正常导入")
            return True
        else:
            print(f"❌ {name} Python模块导入失败:")
            print(f"   错误: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⚠️ {name} 测试超时")
        return False
    except Exception as e:
        print(f"❌ {name} 测试异常: {e}")
        return False

def check_dependencies():
    """检查依赖环境"""
    print("🔍 检查依赖环境...")
    
    # 检查Node.js
    try:
        result = subprocess.run(["node", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js: {result.stdout.strip()}")
        else:
            print("❌ Node.js 未安装或不可用")
            return False
    except FileNotFoundError:
        print("❌ Node.js 未找到")
        return False
    
    # 检查Python
    try:
        result = subprocess.run([sys.executable, "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Python: {result.stdout.strip()}")
        else:
            print("❌ Python 不可用")
            return False
    except Exception:
        print("❌ Python 测试失败")
        return False
    
    return True

def check_project_structure():
    """检查项目结构"""
    print("📁 检查项目结构...")
    
    required_paths = [
        "mcp/servers/src",
        "mcp/servers/src/filesystem/dist",
        "mcp/servers/src/everything/dist", 
        "mcp/servers/src/memory/dist",
        "mcp/servers/src/sequentialthinking/dist"
    ]
    
    all_exist = True
    for path in required_paths:
        if Path(path).exists():
            print(f"✅ 目录存在: {path}")
        else:
            print(f"❌ 目录不存在: {path}")
            all_exist = False
    
    return all_exist

def check_config_files():
    """检查配置文件"""
    print("📄 检查配置文件...")
    
    config_files = [
        ".mcp.json",
        "claude_desktop_config.json",
        "mcp-config.json"
    ]
    
    for config_file in config_files:
        if Path(config_file).exists():
            print(f"✅ 配置文件存在: {config_file}")
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    json.load(f)
                print(f"✅ {config_file} JSON格式正确")
            except json.JSONDecodeError as e:
                print(f"❌ {config_file} JSON格式错误: {e}")
        else:
            print(f"⚠️ 配置文件不存在: {config_file}")

def main():
    """主验证函数"""
    print("🔍 MCP设置验证开始...")
    print("=" * 60)
    
    # 检查基础环境
    if not check_dependencies():
        print("❌ 基础环境检查失败")
        return 1
    
    print()
    
    # 检查项目结构
    if not check_project_structure():
        print("❌ 项目结构检查失败")
        return 1
    
    print()
    
    # 检查配置文件
    check_config_files()
    
    print()
    
    # 测试Node.js服务器
    print("🧪 测试Node.js MCP服务器...")
    node_servers = [
        ("filesystem", "mcp/servers/src/filesystem/dist/index.js"),
        ("everything", "mcp/servers/src/everything/dist/index.js"),
        ("memory", "mcp/servers/src/memory/dist/index.js"),
        ("sequentialthinking", "mcp/servers/src/sequentialthinking/dist/index.js")
    ]
    
    node_success = 0
    for name, path in node_servers:
        if test_node_command(name, path):
            node_success += 1
        print()
    
    # 测试Python服务器
    print("🧪 测试Python MCP服务器...")
    python_servers = [
        ("fetch", "mcp_server_fetch"),
        ("git", "mcp_server_git"),
        ("time", "mcp_server_time")
    ]
    
    python_success = 0
    for name, module in python_servers:
        if test_python_module(name, module):
            python_success += 1
        print()
    
    # 总结结果
    total_node = len(node_servers)
    total_python = len(python_servers)
    total_servers = total_node + total_python
    total_success = node_success + python_success
    
    print("=" * 60)
    print("📊 验证结果:")
    print(f"Node.js服务器: {node_success}/{total_node}")
    print(f"Python服务器: {python_success}/{total_python}")
    print(f"总计: {total_success}/{total_servers}")
    
    if total_success == total_servers:
        print("🎉 所有MCP服务器验证通过！")
        print()
        print("✅ 准备就绪，可以在Claudia中配置MCP服务器")
        print("📖 请参考 MANUAL_MCP_SETUP.md 进行手动配置")
        return 0
    else:
        print("⚠️ 部分服务器验证失败")
        print("🔧 请检查失败的服务器并重新安装依赖")
        return 1

if __name__ == "__main__":
    sys.exit(main())
