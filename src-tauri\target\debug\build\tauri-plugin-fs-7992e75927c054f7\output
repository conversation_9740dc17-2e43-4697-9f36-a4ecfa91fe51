cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=C:\Users\<USER>\Desktop\claudia\src-tauri\target\debug\build\tauri-plugin-fs-7992e75927c054f7\out\tauri-plugin-fs-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_SCOPE_SCHEMA_PATH=C:\Users\<USER>\Desktop\claudia\src-tauri\target\debug\build\tauri-plugin-fs-7992e75927c054f7\out\global-scope.json
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-fs-2.3.0\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
