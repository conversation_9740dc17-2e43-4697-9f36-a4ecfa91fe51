# 📁 Claudia项目文件结构

## 📋 目录说明

### 📖 docs/ - 文档目录
- `mcp/` - MCP服务器相关文档
- `setup/` - 安装和设置文档
- `guides/` - 使用指南

### ⚙️ configs/ - 配置文件
- `mcp/` - MCP服务器配置文件

### 🔧 scripts/ - 脚本文件
- `setup/` - 安装和启动脚本
- `mcp/` - MCP相关脚本

### 🛠️ tools/ - 工具文件
- `import/` - 导入助手工具
- `testing/` - 测试脚本

### 💾 backup/ - 备份文件
- `old-configs/` - 旧配置文件备份

## 🚀 快速开始

1. **启动Claudia**: 运行 `scripts/setup/start-claudia.bat`
2. **配置MCP**: 参考 `docs/mcp/FINAL_MCP_CONFIGURATION.md`
3. **导入服务器**: 使用 `tools/import/import-helper-en.bat`
4. **测试功能**: 运行 `tools/testing/test-all-mcp-servers.py`

## 📚 重要文档

- [MCP配置指南](docs/mcp/FINAL_MCP_CONFIGURATION.md)
- [导入步骤](docs/mcp/STEP_BY_STEP_IMPORT.md)
- [项目总结](docs/mcp/IMPORT_READY_SUMMARY.md)
